index.jsx:189 Uncaught ReferenceError: Cannot access 'handleSubscribe' before initialization
    at BlogArticlesPage (index.jsx:189:36)
    at renderWithHooks (chunk-TAAGB4MZ.js?v=8e8db481:11568:26)
    at updateFunctionComponent (chunk-TAAGB4MZ.js?v=8e8db481:14602:28)
    at mountLazyComponent (chunk-TAAGB4MZ.js?v=8e8db481:14853:23)
    at beginWork (chunk-TAAGB4MZ.js?v=8e8db481:15938:22)
    at HTMLUnknownElement.callCallback2 (chunk-TAAGB4MZ.js?v=8e8db481:3674:22)
    at Object.invokeGuardedCallbackDev (chunk-TAAGB4MZ.js?v=8e8db481:3699:24)
    at invokeGuardedCallback (chunk-TAAGB4MZ.js?v=8e8db481:3733:39)
    at beginWork$1 (chunk-TAAGB4MZ.js?v=8e8db481:19793:15)
    at performUnitOfWork (chunk-TAAGB4MZ.js?v=8e8db481:19226:20)
BlogArticlesPage @ index.jsx:189
renderWithHooks @ chunk-TAAGB4MZ.js?v=8e8db481:11568
updateFunctionComponent @ chunk-TAAGB4MZ.js?v=8e8db481:14602
mountLazyComponent @ chunk-TAAGB4MZ.js?v=8e8db481:14853
beginWork @ chunk-TAAGB4MZ.js?v=8e8db481:15938
callCallback2 @ chunk-TAAGB4MZ.js?v=8e8db481:3674
invokeGuardedCallbackDev @ chunk-TAAGB4MZ.js?v=8e8db481:3699
invokeGuardedCallback @ chunk-TAAGB4MZ.js?v=8e8db481:3733
beginWork$1 @ chunk-TAAGB4MZ.js?v=8e8db481:19793
performUnitOfWork @ chunk-TAAGB4MZ.js?v=8e8db481:19226
workLoopConcurrent @ chunk-TAAGB4MZ.js?v=8e8db481:19217
renderRootConcurrent @ chunk-TAAGB4MZ.js?v=8e8db481:19192
performConcurrentWorkOnRoot @ chunk-TAAGB4MZ.js?v=8e8db481:18706
workLoop @ chunk-TAAGB4MZ.js?v=8e8db481:197
flushWork @ chunk-TAAGB4MZ.js?v=8e8db481:176
performWorkUntilDeadline @ chunk-TAAGB4MZ.js?v=8e8db481:384
index.jsx:189 Uncaught ReferenceError: Cannot access 'handleSubscribe' before initialization
    at BlogArticlesPage (index.jsx:189:36)
    at renderWithHooks (chunk-TAAGB4MZ.js?v=8e8db481:11568:26)
    at updateFunctionComponent (chunk-TAAGB4MZ.js?v=8e8db481:14602:28)
    at mountLazyComponent (chunk-TAAGB4MZ.js?v=8e8db481:14853:23)
    at beginWork (chunk-TAAGB4MZ.js?v=8e8db481:15938:22)
    at HTMLUnknownElement.callCallback2 (chunk-TAAGB4MZ.js?v=8e8db481:3674:22)
    at Object.invokeGuardedCallbackDev (chunk-TAAGB4MZ.js?v=8e8db481:3699:24)
    at invokeGuardedCallback (chunk-TAAGB4MZ.js?v=8e8db481:3733:39)
    at beginWork$1 (chunk-TAAGB4MZ.js?v=8e8db481:19793:15)
    at performUnitOfWork (chunk-TAAGB4MZ.js?v=8e8db481:19226:20)
BlogArticlesPage @ index.jsx:189
renderWithHooks @ chunk-TAAGB4MZ.js?v=8e8db481:11568
updateFunctionComponent @ chunk-TAAGB4MZ.js?v=8e8db481:14602
mountLazyComponent @ chunk-TAAGB4MZ.js?v=8e8db481:14853
beginWork @ chunk-TAAGB4MZ.js?v=8e8db481:15938
callCallback2 @ chunk-TAAGB4MZ.js?v=8e8db481:3674
invokeGuardedCallbackDev @ chunk-TAAGB4MZ.js?v=8e8db481:3699
invokeGuardedCallback @ chunk-TAAGB4MZ.js?v=8e8db481:3733
beginWork$1 @ chunk-TAAGB4MZ.js?v=8e8db481:19793
performUnitOfWork @ chunk-TAAGB4MZ.js?v=8e8db481:19226
workLoopSync @ chunk-TAAGB4MZ.js?v=8e8db481:19165
renderRootSync @ chunk-TAAGB4MZ.js?v=8e8db481:19144
recoverFromConcurrentError @ chunk-TAAGB4MZ.js?v=8e8db481:18764
performConcurrentWorkOnRoot @ chunk-TAAGB4MZ.js?v=8e8db481:18712
workLoop @ chunk-TAAGB4MZ.js?v=8e8db481:197
flushWork @ chunk-TAAGB4MZ.js?v=8e8db481:176
performWorkUntilDeadline @ chunk-TAAGB4MZ.js?v=8e8db481:384
@stagewise_toolbar-react.js?v=8e8db481:3987  GET http://localhost:5747/ping/stagewise net::ERR_CONNECTION_REFUSED
discoverVSCodeWindows @ @stagewise_toolbar-react.js?v=8e8db481:3987
await in discoverVSCodeWindows
discover @ @stagewise_toolbar-react.js?v=8e8db481:4059
(anonymous) @ @stagewise_toolbar-react.js?v=8e8db481:4087
B$1 @ @stagewise_toolbar-react.js?v=8e8db481:193
j$1 @ @stagewise_toolbar-react.js?v=8e8db481:136
setTimeout
r22 @ @stagewise_toolbar-react.js?v=8e8db481:183
requestAnimationFrame
w$1 @ @stagewise_toolbar-react.js?v=8e8db481:185
c.diffed @ @stagewise_toolbar-react.js?v=8e8db481:154
l$1.diffed @ @stagewise_toolbar-react.js?v=8e8db481:487
O @ chunk-BCKZVBEU.js?v=8e8db481:218
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
E @ chunk-BCKZVBEU.js?v=8e8db481:309
initToolbar @ @stagewise_toolbar-react.js?v=8e8db481:11640
(anonymous) @ @stagewise_toolbar-react.js?v=8e8db481:11654
commitHookEffectListMount @ chunk-TAAGB4MZ.js?v=8e8db481:16936
commitPassiveMountOnFiber @ chunk-TAAGB4MZ.js?v=8e8db481:18184
commitPassiveMountEffects_complete @ chunk-TAAGB4MZ.js?v=8e8db481:18157
commitPassiveMountEffects_begin @ chunk-TAAGB4MZ.js?v=8e8db481:18147
commitPassiveMountEffects @ chunk-TAAGB4MZ.js?v=8e8db481:18137
flushPassiveEffectsImpl @ chunk-TAAGB4MZ.js?v=8e8db481:19518
flushPassiveEffects @ chunk-TAAGB4MZ.js?v=8e8db481:19475
(anonymous) @ chunk-TAAGB4MZ.js?v=8e8db481:19356
workLoop @ chunk-TAAGB4MZ.js?v=8e8db481:197
flushWork @ chunk-TAAGB4MZ.js?v=8e8db481:176
performWorkUntilDeadline @ chunk-TAAGB4MZ.js?v=8e8db481:384
@stagewise_toolbar-react.js?v=8e8db481:3987  GET http://localhost:5748/ping/stagewise net::ERR_CONNECTION_REFUSED
discoverVSCodeWindows @ @stagewise_toolbar-react.js?v=8e8db481:3987
await in discoverVSCodeWindows
discover @ @stagewise_toolbar-react.js?v=8e8db481:4059
(anonymous) @ @stagewise_toolbar-react.js?v=8e8db481:4087
B$1 @ @stagewise_toolbar-react.js?v=8e8db481:193
j$1 @ @stagewise_toolbar-react.js?v=8e8db481:136
setTimeout
r22 @ @stagewise_toolbar-react.js?v=8e8db481:183
requestAnimationFrame
w$1 @ @stagewise_toolbar-react.js?v=8e8db481:185
c.diffed @ @stagewise_toolbar-react.js?v=8e8db481:154
l$1.diffed @ @stagewise_toolbar-react.js?v=8e8db481:487
O @ chunk-BCKZVBEU.js?v=8e8db481:218
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
I @ chunk-BCKZVBEU.js?v=8e8db481:79
O @ chunk-BCKZVBEU.js?v=8e8db481:202
E @ chunk-BCKZVBEU.js?v=8e8db481:309
initToolbar @ @stagewise_toolbar-react.js?v=8e8db481:11640
(anonymous) @ @stagewise_toolbar-react.js?v=8e8db481:11654
commitHookEffectListMount @ chunk-TAAGB4MZ.js?v=8e8db481:16936
commitPassiveMountOnFiber @ chunk-TAAGB4MZ.js?v=8e8db481:18184
commitPassiveMountEffects_complete @ chunk-TAAGB4MZ.js?v=8e8db481:18157
commitPassiveMountEffects_begin @ chunk-TAAGB4MZ.js?v=8e8db481:18147
commitPassiveMountEffects @ chunk-TAAGB4MZ.js?v=8e8db481:18137
flushPassiveEffectsImpl @ chunk-TAAGB4MZ.js?v=8e8db481:19518
flushPassiveEffects @ chunk-TAAGB4MZ.js?v=8e8db481:19475
(anonymous) @ chunk-TAAGB4MZ.js?v=8e8db481:19356
workLoop @ chunk-TAAGB4MZ.js?v=8e8db481:197
flushWork @ chunk-TAAGB4MZ.js?v=8e8db481:176
performWorkUntilDeadline @ chunk-TAAGB4MZ.js?v=8e8db481:384
rocket-web.js?_cfg=https%3A%2F%2Fportfolio9495back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.5:1 The above error occurred in the <BlogArticlesPage> component:

    at BlogArticlesPage (http://127.0.0.1:4028/src/pages/blog-articles-page/index.jsx?t=1751080701968:34:37)
    at Routes (http://127.0.0.1:4028/node_modules/.vite/deps/react-router-dom.js?v=8e8db481:730:5)
    at Suspense
    at ErrorBoundary (http://127.0.0.1:4028/src/components/ErrorBoundary.jsx:11:5)
    at Router (http://127.0.0.1:4028/node_modules/.vite/deps/react-router-dom.js?v=8e8db481:677:15)
    at BrowserRouter (http://127.0.0.1:4028/node_modules/.vite/deps/react-router-dom.js?v=8e8db481:1250:5)
    at Routes
    at App

React will try to recreate this component tree from scratch using the error boundary you provided, ErrorBoundary.
(anonymous) @ rocket-web.js?_cfg=https%3A%2F%2Fportfolio9495back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.5:1
setTimeout
console.error @ rocket-web.js?_cfg=https%3A%2F%2Fportfolio9495back.builtwithrocket.new&_be=https%3A%2F%2Fapplication.rocket.new&_v=0.1.5:1
logCapturedError @ chunk-TAAGB4MZ.js?v=8e8db481:14052
callback @ chunk-TAAGB4MZ.js?v=8e8db481:14098
callCallback @ chunk-TAAGB4MZ.js?v=8e8db481:11268
commitUpdateQueue @ chunk-TAAGB4MZ.js?v=8e8db481:11285
commitLayoutEffectOnFiber @ chunk-TAAGB4MZ.js?v=8e8db481:17097
commitLayoutMountEffects_complete @ chunk-TAAGB4MZ.js?v=8e8db481:18008
commitLayoutEffects_begin @ chunk-TAAGB4MZ.js?v=8e8db481:17997
commitLayoutEffects @ chunk-TAAGB4MZ.js?v=8e8db481:17948
commitRootImpl @ chunk-TAAGB4MZ.js?v=8e8db481:19381
commitRoot @ chunk-TAAGB4MZ.js?v=8e8db481:19305
finishConcurrentRender @ chunk-TAAGB4MZ.js?v=8e8db481:18788
performConcurrentWorkOnRoot @ chunk-TAAGB4MZ.js?v=8e8db481:18746
workLoop @ chunk-TAAGB4MZ.js?v=8e8db481:197
flushWork @ chunk-TAAGB4MZ.js?v=8e8db481:176
performWorkUntilDeadline @ chunk-TAAGB4MZ.js?v=8e8db481:384
