# Contributing to Portfolio Pro

First off, thank you for considering contributing to Portfolio Pro! It's people like you that make the open-source community such a great place. We welcome any and all contributions, from bug reports to feature requests and code contributions.

## How to Contribute

We use a standard GitHub flow for all contributions. If you're not familiar with this workflow, here's a quick overview:

1.  **Fork the repository** and clone it to your local machine.
2.  **Create a new branch** for your changes. Please use a descriptive branch name, such as `fix/bug-name` or `feature/new-feature`.
3.  **Make your changes** and commit them with a clear and concise commit message.
4.  **Push your changes** to your forked repository.
5.  **Open a pull request** to the `main` branch of the original repository.

## Reporting Bugs

If you find a bug, please open an issue on our GitHub repository. When you create a bug report, please include the following information:

*   A clear and descriptive title.
*   A detailed description of the bug, including steps to reproduce it.
*   The expected behavior and what actually happened.
*   Any relevant screenshots or error messages.

## Suggesting Features

We're always open to new ideas! If you have a feature you'd like to see in Portfolio Pro, please open an issue on our GitHub repository. When you create a feature request, please include the following information:

*   A clear and descriptive title.
*   A detailed description of the feature, including why you think it would be a good addition.
*   Any relevant mockups or examples.

## Pull Requests

When you're ready to submit a pull request, please make sure you've done the following:

*   **Tested your changes** to ensure they work as expected.
*   **Updated the documentation** to reflect your changes.
*   **Followed the coding style** of the project.
*   **Written a clear and concise pull request description** that explains the changes you've made.

We'll review your pull request as soon as we can. If we have any questions or feedback, we'll leave a comment on the pull request.

Thank you for your contributions!