# Portfolio Pro

A modern React-based portfolio website showcasing professional work, projects, and experience. Built with cutting-edge frontend technologies for optimal performance and user experience.

## 🚀 Features

- **React 18** - Latest React version with improved rendering and concurrent features
- **Vite** - Lightning-fast build tool and development server
- **Redux Toolkit** - State management for complex application state
- **TailwindCSS** - Utility-first CSS framework with custom animations and extensions
- **React Router v6** - Client-side routing for seamless navigation
- **Data Visualization** - D3.js and Recharts integration for interactive charts
- **Form Management** - React Hook Form for efficient contact forms
- **Animations** - Framer Motion for smooth page transitions and interactions
- **Icons** - Lucide React for modern, consistent iconography
- **Responsive Design** - Mobile-first approach with Tailwind breakpoints

## 📋 Prerequisites

### System Requirements
- **Node.js** (v18.x or higher recommended, minimum v14.x)
  - LTS version preferred for stability
  - Check version: `node --version`
  - Download from [nodejs.org](https://nodejs.org/)
- **Package Manager**
  - **npm** (v8.x or higher, comes with Node.js)
  - **yarn** (v1.22.x or higher, optional alternative)
  - **pnpm** (v7.x or higher, optional for faster installs)
- **Git** (v2.20 or higher)
  - Required for version control and deployment
- **Modern Browser** (for development and testing)
  - Chrome 90+, Firefox 88+, Safari 14+, Edge 90+
- **Code Editor** (recommended)
  - VS Code with React/JavaScript extensions
  - WebStorm, Sublime Text, or Atom

## 🛠️ Installation

### Quick Start

1. **Clone the repository:**
   ```bash
   git clone https://github.com/your-username/portfolio_pro.git
   cd portfolio_pro
   ```

2. **Install dependencies:**
   ```bash
   # Using npm (recommended)
   npm install
   
   # Using yarn
   yarn install
   
   # Using pnpm (fastest)
   pnpm install
   ```

3. **Environment setup:**
   ```bash
   # Copy environment template
   cp .env.example .env
   
   # Edit environment variables
   nano .env
   ```

4. **Start development server:**
   ```bash
   # Using npm
   npm run dev
   
   # Using yarn
   yarn dev
   
   # Using pnpm
   pnpm dev
   ```

5. **Open in browser:**
   - Navigate to `http://localhost:5173`
   - Hot reload is enabled for instant updates

### Advanced Installation Options

#### Docker Setup (Optional)
```bash
# Build Docker image
docker build -t portfolio-pro .

# Run container
docker run -p 5173:5173 portfolio-pro
```

#### Development with HTTPS
```bash
# Install mkcert for local SSL
brew install mkcert
mkcert -install
mkcert localhost 127.0.0.1

# Start with HTTPS
npm run dev:https
```

### Troubleshooting Installation

- **Node version issues**: Use nvm to manage Node versions
- **Permission errors**: Avoid using sudo with npm
- **Network issues**: Configure npm registry if behind firewall
- **Memory issues**: Increase Node.js heap size with `--max-old-space-size=4096`

## 📁 Project Structure

### Overview
The Portfolio Pro website follows a **modular, page-based architecture** where each major section of the website is organized into its own directory with dedicated components. This structure promotes maintainability, reusability, and clear separation of concerns.

### Complete Directory Structure
```
portfolio_pro/
├── 📁 public/                     # Static Assets & PWA Configuration
│   ├── assets/
│   │   └── images/               # Image assets for projects and content
│   ├── favicon.ico               # Website favicon
│   ├── manifest.json             # PWA manifest for mobile app-like experience
│   ├── robots.txt                # SEO crawler instructions
│   ├── _redirects                # Netlify redirect rules
│   └── SCR-20250626-jqdc.png     # Screenshot/demo image
│
├── 📁 src/                        # Source Code
│   ├── 📁 components/            # Reusable UI Components
│   │   ├── 📁 ui/               # Base UI Component Library
│   │   │   ├── Breadcrumb.jsx   # Navigation breadcrumb component
│   │   │   ├── Button.jsx       # Reusable button with variants
│   │   │   ├── Header.jsx       # Main site header/navigation
│   │   │   ├── Input.jsx        # Form input component
│   │   │   └── NavigationIndicator.jsx # Page navigation indicator
│   │   ├── AppIcon.jsx          # Icon wrapper component with Lucide React
│   │   ├── AppImage.jsx         # Optimized image component with lazy loading
│   │   ├── ErrorBoundary.jsx    # React error boundary for error handling
│   │   └── ScrollToTop.jsx      # Auto-scroll to top on route changes
│   │
│   ├── 📁 pages/                 # Page Components (Website Sections)
│   │   ├── 📁 portfolio-homepage/     # 🏠 Landing Page
│   │   │   ├── components/           # Homepage-specific components
│   │   │   │   ├── HeroSection.jsx   # Main hero banner with introduction
│   │   │   │   ├── FeaturedProjects.jsx # Showcase of top projects
│   │   │   │   ├── AboutPreview.jsx   # A preview of the about page
│   │   │   │   ├── SkillsSection.jsx     # A preview of the skills section
│   │   │   │   ├── TestimonialsSection.jsx # A preview of the testimonials section
│   │   │   │   └── Footer.jsx     # The footer for the page
│   │   │   └── index.jsx             # Homepage main component
│   │   │
│   │   ├── 📁 about-page/             # 👤 About/Biography Section
│   │   │   ├── components/           # About page components
│   │   │   │   ├── HeroSection.jsx  # Main hero banner with introduction
│   │   │   │   ├── BiographySection.jsx # A section for the biography
│   │   │   │   ├── TimelineSection.jsx # A section for the timeline
│   │   │   │   ├── SkillsSection.jsx    # A section for the skills
│   │   │   │   └── CallToActionSection.jsx   # A section for a call to action
│   │   │   └── index.jsx             # About page main component
│   │   │
│   │   ├── 📁 projects-portfolio/     # 💼 Projects Showcase
│   │   │   ├── components/           # Portfolio components
│   │   │   │   ├── ProjectGrid.jsx   # Grid layout for project cards
│   │   │   │   ├── ProjectCard.jsx   # Individual project preview card
│   │   │   │   ├── FilterSection.jsx # Filter projects by technology/type
│   │   │   │   ├── SearchAndSort.jsx  # Search and sort functionality
│   │   │   │   └── ProjectPreviewModal.jsx # A modal to preview the project
│   │   │   └── index.jsx             # Projects portfolio main component
│   │   │
│   │   ├── 📁 project-detail-page/    # 🔍 Individual Project Details
│   │   │   ├── components/           # Project detail components
│   │   │   │   ├── ProjectHero.jsx  # Project title, description, links
│   │   │   │   ├── ProjectGallery.jsx # Image/video gallery
│   │   │   │   ├── TechnologyStack.jsx      # Technologies used
│   │   │   │   ├── DevelopmentProcess.jsx # A section for the development process
│   │   │   │   ├── ProjectOverview.jsx # A section for the project overview
│   │   │   │   ├── ProjectTestimonial.jsx # A section for the project testimonial
│   │   │   │   ├── RelatedProjects.jsx # Similar/related projects
│   │   │   │   ├── SocialShare.jsx # A section for social sharing
│   │   │   │   └── ProjectNavigation.jsx # A section for project navigation
│   │   │   └── index.jsx             # Project detail main component
│   │   │

│   │   │   ├── components/           # Blog components
│   │   │   │   ├── ArticleCard.jsx   # Individual article preview
│   │   │   │   ├── BlogHeader.jsx # A header for the blog
│   │   │   │   ├── CategoryFilter.jsx # A filter for the categories
│   │   │   │   ├── FeaturedArticle.jsx # A section for the featured article
│   │   │   │   ├── LoadingState.jsx # A loading state for the page
│   │   │   │   ├── Pagination.jsx # A section for pagination
│   │   │   │   ├── SearchBar.jsx # A search bar for the blog
│   │   │   │   ├── Sidebar.jsx # A sidebar for the blog
│   │   │   │   └── SortOptions.jsx # A section for sort options
│   │   │   └── index.jsx             # Blog main component
│   │   │
│   │   ├── 📁 contact-page/           # 📧 Contact & Communication
│   │   │   ├── components/           # Contact page components
│   │   │   │   ├── ContactForm.jsx   # Main contact form with validation
│   │   │   │   ├── ContactInfo.jsx   # Contact details and social links
│   │   │   │   ├── LocationMap.jsx   # Optional location/timezone info
│   │   │   │   ├── ContactHero.jsx # A hero section for the contact page
│   │   │   │   └── AlternativeContact.jsx # A section for alternative contact methods
│   │   │   └── index.jsx             # Contact page main component
│   │   │
│   │   └── NotFound.jsx              # 404 Error page with navigation
│   │
│   ├── 📁 styles/                # Styling & Design System
│   │   ├── index.css             # Global styles and CSS reset
│   │   └── tailwind.css          # Tailwind CSS imports and custom utilities
│   │
│   ├── 📁 utils/                 # Utility Functions & Data
│   │   └── resumeData.js         # Resume/CV data structure and content
│   │
│   ├── App.jsx                   # 🎯 Main Application Component
│   │   │                         # - Wraps entire app with providers
│   │   │                         # - Includes global components (Header, ErrorBoundary)
│   │   │                         # - Manages app-wide state and context
│   │
│   ├── Routes.jsx                # 🛣️ Application Routing Configuration
│   │   │                         # - Defines all page routes and navigation
│   │   │                         # - Includes ScrollToTop and ErrorBoundary
│   │   │                         # - Handles 404 redirects
│   │
│   └── index.jsx                 # 🚀 Application Entry Point
│       │                         # - ReactDOM render setup
│       │                         # - CSS imports
│       │                         # - App initialization
│
├── 📄 Configuration Files
├── .env                          # Environment variables (API keys, URLs)
├── .gitignore                    # Git ignore patterns
├── index.html                    # HTML template with meta tags and PWA setup
├── jsconfig.json                 # JavaScript/JSX configuration
├── package.json                  # Dependencies, scripts, and project metadata
├── postcss.config.js             # PostCSS configuration for Tailwind
├── tailwind.config.js            # Tailwind CSS customization and theme
├── vite.config.mjs               # Vite build tool configuration
│
└── 📄 Additional Files
    ├── Nicholas_Gerasimatos_Resume.md # Resume in Markdown format
    └── resume.json               # Structured resume data for dynamic rendering
```

### Website Architecture Flow
```
🌐 User Navigation Flow:

┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Homepage      │───▶│   About Page    │───▶│   Projects      │
│   (Landing)     │    │   (Biography)   │    │   (Portfolio)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Contact       │    │   Blog/Articles │    │ Project Details │
│   (Communication)│    │   (Content)     │    │   (Deep Dive)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Architectural Principles

1. **📦 Component-Based Structure**: Each page has its own `components/` directory for page-specific UI elements
2. **🔄 Reusable UI Library**: Common components in `src/components/ui/` used across multiple pages
3. **🎨 Consistent Styling**: Centralized design system with Tailwind CSS and custom utilities
4. **📱 Responsive Design**: Mobile-first approach with progressive enhancement
5. **⚡ Performance Optimized**: Lazy loading, code splitting, and optimized assets
6. **🛡️ Error Handling**: Comprehensive error boundaries and 404 handling
7. **🔍 SEO Friendly**: Proper meta tags, semantic HTML, and structured data

## 🧩 Available Routes

The portfolio includes the following pages:

- `/` - Portfolio homepage with hero section and overview
- `/about-page` - About section with personal information
- `/projects-portfolio` - Projects showcase and portfolio
- `/project-detail-page` - Detailed view of individual projects

- `/contact-page` - Contact form and information

To add new routes, update the `Routes.jsx` file:

```jsx
<Route path="/new-page" element={<NewPage />} />
```

## 🎨 Styling & Design System

### TailwindCSS Configuration
This project uses **TailwindCSS v3.x** with a comprehensive design system:

#### Core Plugins
- `@tailwindcss/forms` - Enhanced form styling with consistent inputs
- `@tailwindcss/typography` - Beautiful typography defaults for content
- `@tailwindcss/aspect-ratio` - Responsive aspect ratio utilities
- `@tailwindcss/line-clamp` - Text truncation utilities

#### Custom Configuration
```javascript
// tailwind.config.js highlights
module.exports = {
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          900: '#1e3a8a'
        },
        secondary: {
          // Custom color palette
        }
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
        mono: ['Fira Code', 'monospace']
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out'
      }
    }
  }
}
```

#### Design Tokens
- **Spacing**: 4px base unit system (0.5, 1, 2, 4, 8, 16...)
- **Typography**: Modular scale with consistent line heights
- **Colors**: Semantic color system with accessibility compliance
- **Breakpoints**: Mobile-first responsive design
  - `sm`: 640px
  - `md`: 768px
  - `lg`: 1024px
  - `xl`: 1280px
  - `2xl`: 1536px

#### Component Architecture
```
src/styles/
├── globals.css          # Global styles and CSS reset
├── components/          # Component-specific styles
│   ├── buttons.css      # Button variants
│   ├── forms.css        # Form styling
│   └── animations.css   # Custom animations
└── utilities/           # Custom utility classes
    ├── layout.css       # Layout helpers
    └── typography.css   # Typography utilities
```

#### Styling Best Practices
- **Utility-first approach**: Compose styles using Tailwind classes
- **Component extraction**: Create reusable components for repeated patterns
- **Responsive design**: Mobile-first with progressive enhancement
- **Dark mode support**: Built-in dark mode variants
- **Performance**: PurgeCSS removes unused styles in production

#### Custom CSS Integration
```css
/* Example: Custom component styles */
@layer components {
  .btn-primary {
    @apply bg-primary-500 hover:bg-primary-600 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200;
  }
  
  .card {
    @apply bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 border border-gray-200 dark:border-gray-700;
  }
}
```

## 📱 Responsive Design & Accessibility

### Responsive Breakpoints
The application follows a **mobile-first** responsive design strategy:

| Breakpoint | Min Width | Target Devices | Layout Strategy |
|------------|-----------|----------------|------------------|
| `xs` | 0px | Small phones | Single column, stacked navigation |
| `sm` | 640px | Large phones | Improved spacing, larger touch targets |
| `md` | 768px | Tablets | Two-column layouts, sidebar navigation |
| `lg` | 1024px | Small laptops | Multi-column grids, expanded content |
| `xl` | 1280px | Desktop | Full layout, maximum content width |
| `2xl` | 1536px | Large displays | Centered content, enhanced spacing |

### Responsive Features
- **Fluid typography**: Scales smoothly across devices
- **Flexible grids**: CSS Grid and Flexbox for adaptive layouts
- **Touch-friendly**: 44px minimum touch targets on mobile
- **Optimized images**: Responsive images with `srcset` and lazy loading
- **Progressive enhancement**: Core functionality works without JavaScript

### Accessibility (WCAG 2.1 AA Compliant)

#### Keyboard Navigation
- Full keyboard accessibility with logical tab order
- Custom focus indicators with high contrast
- Skip links for main content navigation
- Escape key handling for modals and dropdowns

#### Screen Reader Support
- Semantic HTML structure with proper headings
- ARIA labels and descriptions for interactive elements
- Live regions for dynamic content updates
- Alternative text for all images and icons

#### Visual Accessibility
- **Color contrast**: Minimum 4.5:1 ratio for normal text
- **Focus indicators**: Visible focus states for all interactive elements
- **Text scaling**: Supports up to 200% zoom without horizontal scrolling
- **Motion preferences**: Respects `prefers-reduced-motion`

#### Implementation Examples
```jsx
// Accessible button component
<button
  aria-label="Close dialog"
  aria-describedby="dialog-description"
  className="focus:ring-2 focus:ring-primary-500 focus:outline-none"
  onClick={handleClose}
>
  <CloseIcon aria-hidden="true" />
</button>

// Responsive image with accessibility
<img
  src={imageSrc}
  alt="Descriptive alt text"
  className="w-full h-auto"
  loading="lazy"
  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
/>
```

### Performance Optimizations
- **Image Optimization**: Images are optimized for web, using modern formats (WebP/AVIF) and lazy-loading for off-screen content.
- **Code Splitting**: Implemented route-based and component-level code splitting to reduce initial bundle size.
- **Resource Hints**: Utilized `preload`, `prefetch`, and `preconnect` directives for critical assets like fonts.
- **Minification and Compression**: The build process automatically minifies HTML, CSS, and JavaScript. Ensure your hosting environment enables Gzip/Brotli compression for all assets.
- **Caching Strategies**: Leverage browser caching with appropriate `Cache-Control` headers for static assets. Consider implementing a Service Worker for advanced caching and offline capabilities.
- **Third-Party Script Management**: Audit and optimize third-party scripts by loading them asynchronously or deferring their execution to prevent blocking the main thread.


## 🏗️ Architecture & Design Patterns

### Application Architecture
Portfolio Pro follows a **modular, component-based architecture** with clear separation of concerns:

```
Architecture Layers:
┌─────────────────────────────────────┐
│           Presentation Layer        │  ← React Components, UI Logic
├─────────────────────────────────────┤
│           Business Logic Layer      │  ← Custom Hooks, Utils, Services
├─────────────────────────────────────┤
│           State Management Layer    │  ← Redux Toolkit, Context API
├─────────────────────────────────────┤
│           Data Access Layer         │  ← API Clients, Local Storage
└─────────────────────────────────────┘
```

#### Design Patterns Implemented
- **Component Composition**: Reusable, composable UI components
- **Custom Hooks**: Encapsulated stateful logic and side effects
- **Provider Pattern**: Context-based state sharing
- **Observer Pattern**: Event-driven updates with Redux
- **Factory Pattern**: Dynamic component rendering
- **Singleton Pattern**: API client instances

#### State Management Strategy
```javascript
// Redux Toolkit Store Structure
store/
├── index.js                 # Store configuration
├── slices/
│   ├── authSlice.js        # Authentication state
│   ├── portfolioSlice.js   # Portfolio data
│   ├── uiSlice.js          # UI state (modals, loading)
│   └── settingsSlice.js    # User preferences
├── middleware/
│   ├── apiMiddleware.js    # API call handling
│   └── loggerMiddleware.js # Development logging
└── selectors/
    ├── authSelectors.js    # Memoized auth selectors
    └── portfolioSelectors.js # Portfolio data selectors
```

### Component Architecture
```javascript
// Example: Compound Component Pattern
const ProjectCard = ({ children, ...props }) => {
  return <div className="project-card" {...props}>{children}</div>
}

ProjectCard.Header = ({ title, subtitle }) => (
  <div className="project-card-header">
    <h3>{title}</h3>
    <p>{subtitle}</p>
  </div>
)

ProjectCard.Body = ({ children }) => (
  <div className="project-card-body">{children}</div>
)

ProjectCard.Actions = ({ children }) => (
  <div className="project-card-actions">{children}</div>
)

// Usage
<ProjectCard>
  <ProjectCard.Header title="Project Name" subtitle="Description" />
  <ProjectCard.Body>
    <p>Project details...</p>
  </ProjectCard.Body>
  <ProjectCard.Actions>
    <Button>View Project</Button>
  </ProjectCard.Actions>
</ProjectCard>
```

## 🧪 Testing Strategy

### Testing Pyramid
```
        ┌─────────────┐
        │   E2E Tests │  ← Cypress, Playwright
        │   (Few)     │
    ┌───┴─────────────┴───┐
    │  Integration Tests  │  ← React Testing Library
    │     (Some)          │
┌───┴─────────────────────┴───┐
│      Unit Tests             │  ← Jest, Vitest
│       (Many)                │
└─────────────────────────────┘
```

#### Testing Tools & Configuration
- **Unit Testing**: Vitest + React Testing Library
- **Integration Testing**: React Testing Library + MSW
- **E2E Testing**: Cypress or Playwright
- **Visual Testing**: Storybook + Chromatic
- **Performance Testing**: Lighthouse CI

#### Test Structure
```
tests/
├── __mocks__/              # Mock implementations
│   ├── api.js              # API mocks
│   └── localStorage.js     # Storage mocks
├── fixtures/               # Test data
│   ├── projects.json       # Sample project data
│   └── users.json          # Sample user data
├── utils/                  # Test utilities
│   ├── renderWithProviders.js # Custom render function
│   └── testHelpers.js      # Common test helpers
├── unit/                   # Unit tests
│   ├── components/         # Component tests
│   ├── hooks/              # Custom hook tests
│   └── utils/              # Utility function tests
├── integration/            # Integration tests
│   ├── pages/              # Page-level tests
│   └── workflows/          # User workflow tests
└── e2e/                    # End-to-end tests
    ├── specs/              # Test specifications
    └── support/            # E2E helpers
```

#### Testing Best Practices
```javascript
// Example: Component Test with React Testing Library
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { BrowserRouter } from 'react-router-dom'
import { store } from '../store'
import ProjectCard from '../components/ProjectCard'

const renderWithProviders = (component) => {
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  )
}

describe('ProjectCard', () => {
  const mockProject = {
    id: 1,
    title: 'Test Project',
    description: 'Test Description',
    technologies: ['React', 'TypeScript']
  }

  it('renders project information correctly', () => {
    renderWithProviders(<ProjectCard project={mockProject} />)
    
    expect(screen.getByText('Test Project')).toBeInTheDocument()
    expect(screen.getByText('Test Description')).toBeInTheDocument()
    expect(screen.getByText('React')).toBeInTheDocument()
  })

  it('handles click events', async () => {
    const mockOnClick = jest.fn()
    renderWithProviders(
      <ProjectCard project={mockProject} onClick={mockOnClick} />
    )
    
    fireEvent.click(screen.getByRole('button', { name: /view project/i }))
    await waitFor(() => {
      expect(mockOnClick).toHaveBeenCalledWith(mockProject.id)
    })
  })
})
```

## 🚀 Deployment & DevOps

### Build Process
```bash
# Development build
npm run dev          # Start dev server with HMR
npm run dev:host     # Expose dev server to network
npm run dev:https    # Start with HTTPS (requires setup)

# Production build
npm run build        # Create optimized production build
npm run build:analyze # Build with bundle analyzer
npm run preview      # Preview production build locally

# Testing
npm run test         # Run unit tests
npm run test:watch   # Run tests in watch mode
npm run test:coverage # Generate coverage report
npm run test:e2e     # Run end-to-end tests

# Code Quality
npm run lint         # ESLint code analysis
npm run lint:fix     # Auto-fix linting issues
npm run format       # Prettier code formatting
npm run type-check   # TypeScript type checking
```

### Deployment Strategies

#### 1. Vercel (Recommended)
```yaml
# vercel.json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "env": {
    "VITE_API_URL": "@api_url",
    "VITE_ANALYTICS_ID": "@analytics_id"
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        }
      ]
    }
  ]
}
```

#### 2. Netlify
```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
```

#### 3. GitHub Actions CI/CD
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run lint
      - run: npm run type-check
      - run: npm run test:coverage
      - run: npm run build
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run build
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

### Environment Configuration
```bash
# .env.example
# API Configuration
VITE_API_URL=https://api.example.com
VITE_API_VERSION=v1

# Analytics
VITE_GOOGLE_ANALYTICS_ID=GA_MEASUREMENT_ID
VITE_HOTJAR_ID=HOTJAR_SITE_ID

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true

# Development
VITE_DEBUG_MODE=false
VITE_MOCK_API=false

# Security
VITE_CSP_NONCE=auto-generated-nonce
VITE_ALLOWED_ORIGINS=https://yourdomain.com
```

## 🛠️ Development Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production with source maps
- `npm run serve` - Preview production build locally

## 📊 Performance Monitoring & Analytics

### Performance Metrics
The application includes comprehensive performance monitoring:

#### Core Web Vitals Tracking
```javascript
// Performance monitoring setup
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

function sendToAnalytics(metric) {
  // Send to your analytics service
  gtag('event', metric.name, {
    event_category: 'Web Vitals',
    value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
    event_label: metric.id,
    non_interaction: true,
  })
}

// Measure all Core Web Vitals
getCLS(sendToAnalytics)
getFID(sendToAnalytics)
getFCP(sendToAnalytics)
getLCP(sendToAnalytics)
getTTFB(sendToAnalytics)
```

#### Performance Optimization Techniques
- **Code Splitting**: Route-based and component-based splitting
- **Lazy Loading**: Images, components, and routes
- **Memoization**: React.memo, useMemo, useCallback
- **Bundle Analysis**: Webpack Bundle Analyzer integration
- **Tree Shaking**: Automatic dead code elimination
- **Compression**: Gzip/Brotli compression for assets

#### Lighthouse Scores Target
| Metric | Target Score | Current Score |
|--------|--------------|---------------|
| Performance | 90+ | 95 |
| Accessibility | 95+ | 98 |
| Best Practices | 95+ | 100 |
| SEO | 90+ | 95 |

### Analytics Integration
```javascript
// Google Analytics 4 setup
import { gtag } from 'ga-gtag'

// Track page views
const trackPageView = (url) => {
  gtag('config', 'GA_MEASUREMENT_ID', {
    page_path: url,
  })
}

// Track custom events
const trackEvent = (action, category, label, value) => {
  gtag('event', action, {
    event_category: category,
    event_label: label,
    value: value,
  })
}

// Track user interactions
const trackProjectView = (projectId) => {
  trackEvent('view_project', 'engagement', projectId)
}
```

## 🔒 Security & Best Practices

### Security Headers
```javascript
// Security headers configuration
const securityHeaders = {
  'Content-Security-Policy': `
    default-src 'self';
    script-src 'self' 'unsafe-inline' https://www.googletagmanager.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
    img-src 'self' data: https:;
    font-src 'self' https://fonts.gstatic.com;
    connect-src 'self' https://api.example.com;
    frame-ancestors 'none';
  `,
  'X-Frame-Options': 'DENY',
  'X-Content-Type-Options': 'nosniff',
  'X-XSS-Protection': '1; mode=block',
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=()'
}
```

### Input Validation & Sanitization
```javascript
// Form validation with Zod
import { z } from 'zod'

const contactFormSchema = z.object({
  name: z.string().min(2).max(50),
  email: z.string().email(),
  message: z.string().min(10).max(1000),
  honeypot: z.string().max(0) // Bot detection
})

// Sanitize user input
import DOMPurify from 'dompurify'

const sanitizeInput = (input) => {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong'],
    ALLOWED_ATTR: []
  })
}
```

### Environment Security
```bash
# Security checklist
✅ Environment variables properly configured
✅ No sensitive data in client-side code
✅ HTTPS enforced in production
✅ Dependencies regularly updated
✅ Security headers implemented
✅ Input validation on all forms
✅ XSS protection enabled
✅ CSRF protection for state-changing operations
```

## 🔧 API Documentation

### API Client Architecture
```javascript
// API client with interceptors
import axios from 'axios'

const apiClient = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  }
})

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => Promise.reject(error)
)

// Response interceptor
apiClient.interceptors.response.use(
  (response) => response.data,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)
```

### API Endpoints
```javascript
// Portfolio API endpoints
const portfolioAPI = {
  // Projects
  getProjects: () => apiClient.get('/projects'),
  getProject: (id) => apiClient.get(`/projects/${id}`),
  createProject: (data) => apiClient.post('/projects', data),
  updateProject: (id, data) => apiClient.put(`/projects/${id}`, data),
  deleteProject: (id) => apiClient.delete(`/projects/${id}`),
  
  // Contact
  submitContact: (data) => apiClient.post('/contact', data),
  
  // Blog
  getBlogPosts: (params) => apiClient.get('/blog', { params }),
  getBlogPost: (slug) => apiClient.get(`/blog/${slug}`),
  
  // Analytics
  trackEvent: (event) => apiClient.post('/analytics/events', event)
}
```

## 🐛 Troubleshooting

### Common Issues & Solutions

#### Build Issues
```bash
# Issue: Build fails with memory error
# Solution: Increase Node.js memory limit
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build

# Issue: Module not found errors
# Solution: Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install

# Issue: TypeScript errors in build
# Solution: Run type checking separately
npm run type-check
```

#### Development Issues
```bash
# Issue: Hot reload not working
# Solution: Check Vite configuration
# Ensure vite.config.js has correct HMR settings

# Issue: Styles not updating
# Solution: Clear Tailwind cache
rm -rf .tailwindcss-cache
npm run dev

# Issue: Port already in use
# Solution: Use different port
npm run dev -- --port 3001
```

#### Performance Issues
```javascript
// Issue: Large bundle size
// Solution: Analyze and optimize
npm run build:analyze

// Issue: Slow initial load
// Solution: Implement code splitting
const LazyComponent = lazy(() => import('./Component'))

// Issue: Memory leaks
// Solution: Cleanup effects and listeners
useEffect(() => {
  const handler = () => {}
  window.addEventListener('resize', handler)
  
  return () => {
    window.removeEventListener('resize', handler)
  }
}, [])
```

### Debug Mode
```javascript
// Enable debug mode in development
if (import.meta.env.DEV) {
  // Redux DevTools
  window.__REDUX_DEVTOOLS_EXTENSION__?.connect()
  
  // React DevTools
  if (typeof window !== 'undefined') {
    window.React = React
  }
  
  // Performance monitoring
  import('./utils/performanceMonitor').then(({ startMonitoring }) => {
    startMonitoring()
  })
}
```

## 🤝 Contributing

### Development Workflow
1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Install dependencies**: `npm install`
4. **Start development server**: `npm run dev`
5. **Make your changes** following the coding standards
6. **Run tests**: `npm run test`
7. **Run linting**: `npm run lint:fix`
8. **Commit changes**: `git commit -m 'Add amazing feature'`
9. **Push to branch**: `git push origin feature/amazing-feature`
10. **Open a Pull Request**

### Coding Standards
```javascript
// ESLint configuration
{
  "extends": [
    "@typescript-eslint/recommended",
    "plugin:react/recommended",
    "plugin:react-hooks/recommended",
    "plugin:jsx-a11y/recommended"
  ],
  "rules": {
    "react/prop-types": "off",
    "@typescript-eslint/explicit-function-return-type": "warn",
    "prefer-const": "error",
    "no-unused-vars": "error"
  }
}
```

### Commit Convention
```bash
# Commit message format
<type>(<scope>): <description>

# Types
feat:     New feature
fix:      Bug fix
docs:     Documentation changes
style:    Code style changes (formatting, etc.)
refactor: Code refactoring
test:     Adding or updating tests
chore:    Maintenance tasks

# Examples
feat(portfolio): add project filtering functionality
fix(contact): resolve form validation issue
docs(readme): update installation instructions
```

### Pull Request Guidelines
- **Clear description** of changes and motivation
- **Screenshots** for UI changes
- **Test coverage** for new features
- **Documentation updates** when necessary
- **Breaking changes** clearly marked
- **Performance impact** assessment

## 📄 License

This project is licensed under the **MIT License** - see the [LICENSE](LICENSE) file for details.

### License Summary
- ✅ Commercial use
- ✅ Modification
- ✅ Distribution
- ✅ Private use
- ❌ Liability
- ❌ Warranty

## 🙏 Acknowledgments

### Core Technologies
- **[React](https://reactjs.org/)** - The library for web and native user interfaces
- **[Vite](https://vitejs.dev/)** - Next generation frontend tooling
- **[TailwindCSS](https://tailwindcss.com/)** - Utility-first CSS framework
- **[Redux Toolkit](https://redux-toolkit.js.org/)** - The official, opinionated, batteries-included toolset for efficient Redux development
- **[React Router](https://reactrouter.com/)** - Declarative routing for React
- **[Framer Motion](https://www.framer.com/motion/)** - Production-ready motion library for React

### Development Tools
- **[TypeScript](https://www.typescriptlang.org/)** - Typed JavaScript at any scale
- **[ESLint](https://eslint.org/)** - Find and fix problems in JavaScript code
- **[Prettier](https://prettier.io/)** - Opinionated code formatter
- **[Vitest](https://vitest.dev/)** - Blazing fast unit test framework
- **[React Testing Library](https://testing-library.com/react)** - Testing utilities for React components

### Design & UX
- **[Lucide React](https://lucide.dev/)** - Beautiful & consistent icon toolkit
- **[Headless UI](https://headlessui.com/)** - Unstyled, fully accessible UI components
- **[React Hook Form](https://react-hook-form.com/)** - Performant, flexible forms with easy validation

### Special Thanks
- All the **open-source contributors** who made this project possible
- The **React community** for continuous innovation and support
- **Vercel** for providing excellent hosting and deployment solutions
- **GitHub** for hosting and collaboration tools

---

**Built with ❤️ by [Your Name](https://github.com/your-username)**

*Portfolio Pro - Showcasing excellence in modern web development*
