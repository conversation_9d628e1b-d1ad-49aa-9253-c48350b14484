/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');

/* Import Tailwind CSS directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base layer for custom CSS properties and global styles */
@layer base {
  :root {
    /* Primary Colors: Used for main branding elements, prominent CTAs, and key text. */
    --color-primary: #1a1a1a; /* deep-charcoal */
    --color-primary-foreground: #ffffff; /* white */
    
    /* Secondary Colors: Used for less prominent actions, secondary backgrounds, or subtle accents. */
    --color-secondary: #4a5568; /* sophisticated-gray */
    --color-secondary-foreground: #ffffff; /* white */
    
    /* Accent Colors: Used for interactive elements, highlights, and to draw attention. */
    --color-accent: #3182ce; /* professional-blue */
    --color-accent-foreground: #ffffff; /* white */
    
    /* Background Colors: Define the main background and elevated surface colors. */
    --color-background: #ffffff; /* pure-white */
    --color-surface: #f7fafc; /* subtle-off-white */
    
    /* Text Colors: For various levels of text hierarchy. */
    --color-text-primary: #2d3748; /* rich-dark-gray */
    --color-text-secondary: #718096; /* balanced-medium-gray */
    
    /* Status Colors: For conveying success, warning, and error states. */
    --color-success: #38a169; /* professional-green */
    --color-success-foreground: #ffffff; /* white */
    --color-warning: #d69e2e; /* sophisticated-amber */
    --color-warning-foreground: #ffffff; /* white */
    --color-error: #e53e3e; /* clear-red */
    --color-error-foreground: #ffffff; /* white */
    
    /* Border and Shadow Colors: For defining borders and various shadow depths. */
    --color-border: rgba(0, 0, 0, 0.1); /* subtle-border */
    --color-shadow-light: rgba(0, 0, 0, 0.1); /* light-shadow */
    --color-shadow-medium: rgba(0, 0, 0, 0.15); /* medium-shadow */
    
    /* Animation Durations: Standardized transition durations for smooth UI changes. */
    --transition-fast: 200ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Apply border color to all elements by default */
  * {
    @apply border-border;
  }
  
  /* Set global body styles and font properties */
  body {
    @apply bg-background text-text-primary font-body;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
  
  /* Apply heading font to all heading elements */
  h1, h2, h3, h4, h5, h6 {
    @apply font-heading;
  }
}

/* Components layer for custom component-specific styles */
@layer components {
  /* Shadow for navigation elements */
  .nav-shadow {
    box-shadow: 0 1px 3px var(--color-shadow-light);
  }
  
  /* Shadow for card elements */
  .card-shadow {
    box-shadow: 0 1px 3px var(--color-shadow-light);
  }
  
  /* Shadow for interactive elements */
  .interactive-shadow {
    box-shadow: 0 4px 6px var(--color-shadow-light);
  }
  
  /* Smooth transition for general elements */
  .transition-smooth {
    transition: all var(--transition-fast);
  }
  
  /* Transition for layout-related changes */
  .transition-layout {
    transition: all var(--transition-normal);
  }
}