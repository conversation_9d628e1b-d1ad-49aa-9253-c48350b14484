import React from 'react';

/**
 * A reusable Image component that provides a fallback for broken image links.
 *
 * @param {Object} props - The component props.
 * @param {string} props.src - The source URL of the image.
 * @param {string} [props.alt="Image Name"] - The alt text for the image.
 * @param {string} [props.className=""] - Additional CSS classes to apply to the image.
 * @param {Object} rest - Additional props to be passed to the underlying `<img>` element.
 * @returns {JSX.Element} The rendered `<img>` element.
 */
function Image({
  src,
  alt = "Image Name",
  className = "",
  loading = "lazy", // Default to lazy loading
  srcSet, // For responsive images
  sizes,  // For responsive images
  ...props
}) {

  return (
    <img
      src={src}
      alt={alt}
      className={className}
      loading={loading}
      srcSet={srcSet}
      sizes={sizes}
      onError={(e) => {
        // Fallback to a placeholder image if the original image fails to load
        e.target.src = "/assets/images/no_image.png"
      }}
      {...props}
    />
  );
}

export default Image;
