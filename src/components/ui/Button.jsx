import React from 'react';
import Icon from 'components/AppIcon';

/**
 * A highly customizable Button component.
 * It supports various visual variants, sizes, shapes, and can display icons or a loading spinner.
 *
 * @param {Object} props - The component props.
 * @param {React.ReactNode} props.children - The content to be rendered inside the button.
 * @param {'primary' | 'secondary' | 'success' | 'danger' | 'warning' | 'info' | 'ghost' | 'link' | 'outline' | 'text'} [props.variant='primary'] - The visual style of the button.
 * @param {'2xs' | 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'} [props.size='md'] - The size of the button.
 * @param {'rounded' | 'square' | 'pill' | 'circle'} [props.shape='rounded'] - The shape of the button.
 * @param {boolean} [props.fullWidth=false] - If true, the button will take up the full width of its parent.
 * @param {boolean} [props.disabled=false] - If true, the button will be disabled.
 * @param {boolean} [props.loading=false] - If true, a loading spinner will be displayed and the button will be disabled.
 * @param {React.ReactElement} [props.icon=null] - A React element to be rendered as an icon.
 * @param {string} [props.iconName=null] - The name of an icon to be rendered using the `AppIcon` component.
 * @param {'left' | 'right'} [props.iconPosition='left'] - The position of the icon relative to the children.
 * @param {'button' | 'submit' | 'reset'} [props.type='button'] - The type of the button.
 * @param {number} [props.iconSize=null] - The size of the icon. Overrides default size based on button size.
 * @param {string} [props.iconColor=null] - The color of the icon. Overrides default color.
 * @param {string} [props.className=''] - Additional CSS classes to apply to the button.
 * @param {function} [props.onClick] - The click event handler for the button.
 * @param {Object} rest - Additional props to be passed to the underlying button element.
 * @param {React.Ref} ref - A ref to the underlying button element.
 * @returns {JSX.Element} The rendered button element.
 */
const Button = React.forwardRef(({
    children,
    variant = 'primary',
    size = 'md',
    shape = 'rounded',
    fullWidth = false,
    disabled = false,
    loading = false,
    icon = null,
    iconName = null,
    iconPosition = 'left',
    type = 'button',
    iconSize = null,
    iconColor = null,
    className = '',
    onClick,
    ...rest
}, ref) => {
    // Base classes for all buttons
    const baseClasses = 'inline-flex items-center justify-center transition-all duration-200 font-medium focus:ring-2 focus:outline-none';

    // Classes based on button size
    const sizeClasses = {
        '2xs': 'text-xs py-0.5 px-1.5',
        xs: 'text-xs py-1 px-2',
        sm: 'text-sm py-1.5 px-3',
        md: 'text-base py-2 px-4',
        lg: 'text-lg py-2.5 px-5',
        xl: 'text-xl py-3 px-6',
        '2xl': 'text-2xl py-4 px-8',
    };

    // Classes based on button shape
    const shapeClasses = {
        rounded: 'rounded',
        square: 'rounded-none',
        pill: 'rounded-full',
        circle: 'rounded-full aspect-square',
    };

    // Classes based on button variant
    const variantClasses = {
        primary: 'bg-primary text-primary-foreground hover:bg-primary/90',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        success: 'bg-success text-white hover:bg-opacity-90 active:bg-opacity-100 disabled:opacity-50',
        danger: 'bg-error text-white hover:bg-opacity-90 active:bg-opacity-100 disabled:opacity-50',
        warning: 'bg-warning text-primary-foreground hover:bg-opacity-90 active:bg-opacity-100 disabled:opacity-50',
        info: 'bg-accent text-white hover:bg-opacity-90 active:bg-opacity-100 disabled:opacity-50',
        ghost: 'hover:bg-primary hover:text-primary-foreground',
        link: 'bg-transparent text-primary-foreground underline hover:text-primary-600 p-0',
        outline: 'border border-input bg-background hover:bg-primary hover:text-primary-foreground',
        text: 'bg-transparent text-primary-foreground hover:bg-surface hover:bg-opacity-50 active:bg-surface active:bg-opacity-70',
    };

    // Width classes based on fullWidth prop
    const widthClasses = fullWidth ? 'w-full' : '';

    // Classes for disabled state
    const disabledClasses = disabled ? 'cursor-not-allowed opacity-60' : '';

    // Loading spinner content
    const loadingContent = loading ? (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
    ) : null;

    // Function to render the icon
    const renderIcon = () => {
        if (iconName) {
            // Use AppIcon component when iconName is provided
            const iconSizeMap = {
                '2xs': 12,
                xs: 14,
                sm: 16,
                md: 18,
                lg: 20,
                xl: 22,
                '2xl': 24,
            };

            const calculatedSize = iconSize || iconSizeMap[size] || 18;

            return (
                <span style={{ color: iconColor || 'currentColor' }}>
                    <Icon
                        name={iconName}
                        size={calculatedSize}
                        className={`${children ? (iconPosition === 'left' ? 'mr-2' : 'ml-2') : ''}`}
                    />
                </span>

            );
        }

        if (!icon) return null;

        return React.cloneElement(icon, {
            className: `${children ? (iconPosition === 'left' ? 'mr-2' : 'ml-2') : ''} h-5 w-5`
        });
    };

    // Combine all classes using template literals
    const classes = `
        ${baseClasses}
        ${sizeClasses[size] || sizeClasses.md}
        ${shapeClasses[shape] || shapeClasses.rounded}
        ${variantClasses[variant] || variantClasses.primary}
        ${widthClasses}
        ${disabledClasses}
        ${className}
    `;

    return (
        <button
            ref={ref}
            type={type}
            className={classes}
            disabled={disabled || loading}
            onClick={onClick}
            {...rest}
        >
            {loading && loadingContent}
            {(icon || iconName) && iconPosition === 'left' && renderIcon()}
            {children}
            {(icon || iconName) && iconPosition === 'right' && renderIcon()}
        </button>
    );
});

export default Button;