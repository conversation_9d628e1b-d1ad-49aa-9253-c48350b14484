import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

/**
 * NavigationIndicator component displays a set of circular indicators on the side of the page.
 * These indicators represent different sections of the page and highlight the currently visible section.
 * Clicking an indicator scrolls the user smoothly to the corresponding section.
 *
 * @param {Object} props - The component props.
 * @param {Array<Object>} props.sections - An array of section objects, each with an `id` and `label`.
 */
const NavigationIndicator = ({ sections = [] }) => {
  const [activeSection, setActiveSection] = useState('');
  const location = useLocation(); // Used to re-observe sections on route changes

  useEffect(() => {
    // Options for the Intersection Observer
    const observerOptions = {
      root: null, // Use the viewport as the root
      rootMargin: '-20% 0px -80% 0px', // Adjust top and bottom margins to trigger intersection earlier/later
      threshold: 0, // Trigger when any part of the target is visible
    };

    // Callback function for the Intersection Observer
    const observerCallback = (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          setActiveSection(entry.target.id);
        }
      });
    };

    // Create a new Intersection Observer instance
    const observer = new IntersectionObserver(observerCallback, observerOptions);

    // Observe each section element
    sections.forEach((section) => {
      const element = document.getElementById(section.id);
      if (element) {
        observer.observe(element);
      }
    });

    // Cleanup function: disconnect the observer when the component unmounts or sections change
    return () => {
      sections.forEach((section) => {
        const element = document.getElementById(section.id);
        if (element) {
          observer.unobserve(element);
        }
      });
    };
  }, [sections, location.pathname]); // Re-run effect if sections or pathname change

  // If no sections are provided, don't render the component
  if (!sections.length) {
    return null;
  }

  /**
   * Scrolls the window smoothly to the specified section.
   * @param {string} sectionId - The ID of the section to scroll to.
   */
  const scrollToSection = (sectionId) => {
    const element = document.getElementById(sectionId);
    if (element) {
      const headerOffset = 100; // Offset to account for fixed header
      const elementPosition = element.getBoundingClientRect().top;
      const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
    }
  };

  return (
    <div className="fixed right-6 top-1/2 transform -translate-y-1/2 z-150 hidden lg:block">
      <nav 
        className="bg-background/95 backdrop-blur-sm rounded-lg shadow-card border border-border p-2"
        aria-label="Page sections"
      >
        <ul className="space-y-2">
          {sections.map((section) => (
            <li key={section.id}> {/* Unique key for each list item */}
              <button
                onClick={() => scrollToSection(section.id)}
                className={`w-3 h-3 rounded-full border transition-all duration-300 relative group ${
                  activeSection === section.id
                    ? 'bg-text-primary scale-125 border-text-primary' // Active state styles
                    : 'bg-transparent border-text-secondary/30 hover:border-text-primary/60 hover:scale-110' // Inactive state styles
                }`}
                aria-label={`Go to ${section.label} section`}
                title={section.label} // Tooltip for accessibility
              >
                {/* Tooltip text that appears on hover */}
                <span className="absolute right-full top-1/2 transform -translate-y-1/2 mr-3 bg-background text-text-primary text-xs px-3 py-1.5 rounded-md shadow-md whitespace-nowrap opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none border border-border">
                  {section.label}
                </span>
              </button>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default NavigationIndicator;