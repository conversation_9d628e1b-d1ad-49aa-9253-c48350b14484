import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import Icon from '../AppIcon';

/**
 * Breadcrumb component displays the current page's location within the application's hierarchy.
 * It dynamically generates breadcrumb links based on the current URL and a predefined map.
 *
 * @returns {JSX.Element|null} The rendered breadcrumb navigation or null if no breadcrumbs are needed.
 */
const Breadcrumb = () => {
  const location = useLocation();
  
  /**
   * Defines the mapping of paths to their breadcrumb labels and parent paths.
   * This map is used to construct the breadcrumb trail.
   */
  const breadcrumbMap = {
    '/portfolio-homepage': { label: 'Home', parent: null },
    '/about-page': { label: 'About', parent: '/portfolio-homepage' },
    '/projects-portfolio': { label: 'Projects', parent: '/portfolio-homepage' },
    '/project-detail-page': { label: 'Project Details', parent: '/projects-portfolio' },
    '/contact-page': { label: 'Contact', parent: '/portfolio-homepage' },
    
  };

  const currentPath = location.pathname;
  const currentBreadcrumb = breadcrumbMap[currentPath];

  // Do not render breadcrumbs if the current path is the homepage or not found in the map
  if (!currentBreadcrumb || currentPath === '/portfolio-homepage') {
    return null;
  }

  /**
   * Recursively builds the breadcrumb path from the current path to the root.
   * @param {string} path - The current path to start building the breadcrumb from.
   * @returns {Array<Object>} An array of breadcrumb objects, each with a path and label.
   */
  const buildBreadcrumbPath = (path) => {
    const breadcrumbs = [];
    let current = breadcrumbMap[path];
    let currentPath = path;

    while (current) {
      breadcrumbs.unshift({ path: currentPath, label: current.label });
      if (current.parent) {
        currentPath = current.parent;
        current = breadcrumbMap[current.parent];
      } else {
        break;
      }
    }

    return breadcrumbs;
  };

  const breadcrumbs = buildBreadcrumbPath(currentPath);

  return (
    <nav 
      className="flex items-center space-x-2 text-sm text-text-secondary mb-6"
      aria-label="Breadcrumb navigation"
    >
      <ol className="flex items-center space-x-2">
        {breadcrumbs.map((breadcrumb, index) => (
          <li key={breadcrumb.path} className="flex items-center space-x-2">
            {/* Render chevron icon between breadcrumb items */}
            {index > 0 && (
              <Icon 
                name="ChevronRight" 
                size={14} 
                className="text-text-secondary/60" 
              />
            )}
            {/* Render current page as a span, others as links */}
            {index === breadcrumbs.length - 1 ? (
              <span 
                className="text-text-primary font-medium"
                aria-current="page"
              >
                {breadcrumb.label}
              </span>
            ) : (
              <Link
                to={breadcrumb.path}
                className="hover:text-text-primary transition-smooth"
              >
                {breadcrumb.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb;