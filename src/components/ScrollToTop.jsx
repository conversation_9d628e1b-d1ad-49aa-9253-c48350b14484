import { useEffect } from "react";
import { useLocation } from "react-router-dom";

/**
 * ScrollToTop component.
 *
 * This component listens to route changes and scrolls the window to the top
 * whenever the pathname changes. It's useful for single-page applications
 * where navigation doesn't trigger a full page reload.
 *
 * @returns {null} This component does not render anything.
 */
const ScrollToTop = () => {
  const { pathname } = useLocation();

  useEffect(() => {
    // Scroll to the top of the window whenever the pathname changes
    window.scrollTo(0, 0);
  }, [pathname]); // Dependency array: effect runs when `pathname` changes

  return null; // This component does not render any UI
};

export default ScrollToTop;