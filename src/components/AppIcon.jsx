import React from 'react';
import * as LucideIcons from 'lucide-react';
import { HelpCircle } from 'lucide-react';

/**
 * A dynamic icon component that renders icons from the `lucide-react` library.
 * If the specified icon name is not found, it defaults to a `HelpCircle` icon.
 *
 * @param {Object} props - The component props.
 * @param {string} props.name - The name of the icon to render (e.g., "Home", "User").
 * @param {number} [props.size=24] - The size of the icon in pixels.
 * @param {string} [props.color="currentColor"] - The color of the icon.
 * @param {string} [props.className=""] - Additional CSS classes to apply to the icon.
 * @param {number} [props.strokeWidth=2] - The stroke width of the icon.
 * @param {Object} rest - Additional props to be passed to the underlying icon component.
 * @returns {JSX.Element} The rendered icon component.
 */
function Icon({
    name,
    size = 24,
    color = "currentColor",
    className = "",
    strokeWidth = 2,
    ...props
}) {
    // Dynamically get the icon component from LucideIcons based on the provided name
    const IconComponent = LucideIcons[name];

    // If the icon component is not found, render a HelpCircle icon as a fallback
    if (!IconComponent) {
        return <HelpCircle size={size} color="gray" strokeWidth={strokeWidth} className={className} {...props} />;
    }

    // Render the requested icon component with the provided props
    return <IconComponent
        size={size}
        color={color}
        strokeWidth={strokeWidth}
        className={className}
        {...props}
    />;
}
export default Icon;