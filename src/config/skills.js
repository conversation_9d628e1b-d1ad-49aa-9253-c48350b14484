export const technicalSkills = [
  // Cloud Platforms
  'Amazon Web Services (AWS)', 'Microsoft Azure', 'Google Cloud Platform (GCP)',
  'OpenStack', 'Red Hat OpenShift', 'Amazon EKS', 'Google Kubernetes Engine (GKE)', 'IBM Cloud',
  
  // Containerization & Orchestration
  'Kubernetes', 'Docker', 'Containers', 'Pods', 'Operators', 'Helm', 'Kustomize',
  'OpenShift', 'Amazon EKS', 'Google Kubernetes Engine (GKE)', 'AKS', 'Rancher',
  
  // Infrastructure as Code
  'Terraform', 'AWS CloudFormation', 'Azure Resource Manager', 'Pulumi', 'Crossplane',
  
  // CI/CD & DevOps
  'GitOps', 'CI/CD', 'DevOps', 'ArgoCD', 'Jenkins', 'GitHub Actions', 'GitLab CI/CD',
  'Tekton', 'Flux', 'CircleCI', 'Travis CI', 'Spinnaker',
  
  // Monitoring & Observability
  'Prometheus', 'Grafana', 'ELK Stack', 'OpenTelemetry', 'Jaeger', 'New Relic',
  'Datadog', 'Splunk', 'Dynatrace', 'Loki', 'Thanos',
  
  // Operating Systems
  'Linux', 'Red Hat Enterprise Linux (RHEL)', 'Ubuntu', 'CoreOS', 'Flatcar',
  'Windows Server', 'SUSE', 'CentOS', 'Fedora', 'Alpine',
  
  // Virtualization
  'VMware', 'KVM', 'QEMU', 'Hyper-V', 'VirtualBox', 'Xen', 'Proxmox',
  
  // Security
  'Zero Trust Architecture', 'mTLS', 'SPIFFE/SPIRE', 'Vault', 'Keycloak', 'OAuth2',
  'OpenID Connect', 'Istio Security', 'OPA Gatekeeper', 'Falco', 'Trivy', 'Aqua Security'
];

export const cloudSkills = [
  // Cloud Services
  'Amazon Bedrock', 'AWS Lambda', 'AWS ECS', 'AWS Fargate', 'AWS S3', 'AWS RDS',
  'AWS EBS', 'AWS EFS', 'AWS IAM', 'AWS VPC', 'AWS Direct Connect', 'AWS Transit Gateway',
  'Azure Functions', 'Azure Kubernetes Service', 'Azure Blob Storage', 'Azure SQL',
  'Google Cloud Functions', 'Google Kubernetes Engine', 'Cloud Run', 'Cloud SQL',
  
  // Cloud Patterns
  'Cloud Native Architecture', 'Serverless', 'Function as a Service (FaaS)',
  'Containers as a Service (CaaS)', 'Platform as a Service (PaaS)',
  'Infrastructure as a Service (IaaS)', 'Hybrid Cloud', 'Multi-Cloud', 'Private Cloud',
  'Cloud Migration', 'Cloud Cost Optimization', 'Cloud Networking', 'Cloud Security',
  'Cloud Storage', 'Cloud Databases', 'Cloud Identity', 'Cloud Automation',
  'Cloud Governance', 'Cloud Compliance', 'Disaster Recovery as a Service (DRaaS)',
  'Backup as a Service (BaaS)'
];

export const aiMlSkills = [
  // AI/ML Frameworks
  'TensorFlow', 'PyTorch', 'Keras', 'scikit-learn', 'ONNX', 'TensorRT', 'MLflow',
  'Kubeflow', 'MLflow', 'SageMaker', 'Vertex AI', 'Azure ML', 'Hugging Face',
  'LangChain', 'LlamaIndex', 'OpenAI API', 'Anthropic Claude', 'Google Gemini',
  
  // AI/ML Concepts
  'Artificial Intelligence (AI)', 'Machine Learning (ML)', 'Deep Learning',
  'Neural Networks', 'Computer Vision', 'Natural Language Processing (NLP)',
  'Large Language Models (LLMs)', 'Generative AI', 'Reinforcement Learning',
  'Transfer Learning', 'Model Fine-tuning', 'Model Serving', 'Model Monitoring',
  'Feature Engineering', 'Data Labeling', 'MLOps', 'ML Pipelines', 'Model Registry',
  'Model Versioning', 'A/B Testing', 'Model Explainability', 'AI Ethics', 'Responsible AI'
];

export const architectureSkills = [
  // System Design
  'Distributed Systems', 'Microservices', 'Service Mesh', 'Event-Driven Architecture',
  'API Gateway', 'Service Discovery', 'Circuit Breaker', 'Bulkhead', 'Retry Patterns',
  'CQRS', 'Event Sourcing', 'Saga Pattern', 'Sidecar Pattern', 'Ambassador Pattern',
  'Backends for Frontends (BFF)', 'API Design', 'REST', 'gRPC', 'GraphQL', 'WebSockets',
  
  // Data Architecture
  'Data Mesh', 'Data Fabric', 'Data Lake', 'Data Warehouse', 'Data Lakehouse',
  'Data Pipeline', 'ETL/ELT', 'Change Data Capture (CDC)', 'Stream Processing',
  'Batch Processing', 'Real-time Analytics', 'Time-series Data', 'Graph Databases',
  'Vector Databases', 'Data Catalog', 'Data Governance', 'Data Quality',
  'Master Data Management (MDM)', 'Data Lineage', 'Data Privacy', 'Data Security',
  
  // Infrastructure Patterns
  'Infrastructure as Code (IaC)', 'GitOps', 'ChatOps', 'FinOps', 'DevSecOps',
  'Platform Engineering', 'Internal Developer Platform (IDP)', 'Service Catalog',
  'Self-Service Infrastructure', 'Multi-tenancy', 'Multi-region', 'Active-Active',
  'Disaster Recovery', 'High Availability', 'Fault Tolerance', 'Chaos Engineering',
  'Performance Engineering', 'Capacity Planning', 'Cost Optimization',
  'Green Computing', 'Sustainable Architecture'
];

export const managementSkills = [
  // Leadership
  'Technical Leadership', 'Team Leadership', 'Mentoring', 'Coaching', 'Agile Coaching',
  'Stakeholder Management', 'Vendor Management', 'Partner Management',
  'Business Development', 'Pre-sales Engineering', 'Solution Architecture',
  'Technical Consulting', 'Professional Services', 'Customer Success',
  
  // Project Management
  'Agile', 'Scrum', 'Kanban', 'SAFe', 'Lean', 'DevOps', 'Site Reliability Engineering (SRE)',
  'Project Planning', 'Roadmapping', 'Prioritization', 'Risk Management',
  'Stakeholder Communication', 'Status Reporting', 'Budget Management',
  'Resource Allocation', 'Capacity Planning', 'Release Management',
  
  // Technical Strategy
  'Cloud Strategy', 'Digital Transformation', 'Platform Strategy', 'Data Strategy',
  'AI Strategy', 'Security Strategy', 'Compliance Strategy', 'Cost Optimization',
  'Technical Due Diligence', 'Architecture Review', 'Code Review',
  'Performance Review', 'Incident Management', 'Post-Mortem Analysis',
  'Root Cause Analysis', 'Continuous Improvement', 'Process Optimization',
  'Knowledge Sharing', 'Documentation', 'Technical Writing', 'Public Speaking',
  'Workshop Facilitation', 'Training Development'
];