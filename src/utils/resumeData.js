import resumeJson from '../../resume.json';
import { technicalSkills, cloudSkills, aiMlSkills, architectureSkills, managementSkills } from '../config/skills';

/**
 * Utility functions to access and format resume data
 */
export const getResumeData = () => {
  return resumeJson;
};

export const getBasicInfo = () => {
  return resumeJson.basics;
};

export const getWorkExperience = () => {
  return resumeJson.work.map((job, index) => ({
    id: index + 1,
    position: job.position,
    company: job.name,
    startDate: job.startDate,
    endDate: job.endDate,
    location: job.location || 'Remote',
    summary: job.summary,
    highlights: job.highlights || [],
    url: job.url,
    technologies: job.technologies || []
  }));
};

export const getSkills = () => {
  return {
    technical: {
      title: 'Cloud & Infrastructure',
      icon: 'Cloud',
      skills: technicalSkills.map(skill => ({
        name: skill,
        level: getSkillLevel(skill),
        category: 'Technical',
        years: getSkillYears(skill)
      }))
    },
    cloud: {
      title: 'Platform Engineering',
      icon: 'Server',
      skills: cloudSkills.map(skill => ({
        name: skill,
        level: getSkillLevel(skill),
        category: 'Cloud',
        years: getSkillYears(skill)
      }))
    },
    ai: {
      title: 'AI & Machine Learning',
      icon: 'Brain',
      skills: aiMlSkills.map(skill => ({
        name: skill,
        level: getSkillLevel(skill),
        category: 'AI/ML',
        years: getSkillYears(skill)
      }))
    },
    architecture: {
      title: 'Architecture & Design',
      icon: 'Layout',
      skills: architectureSkills.map(skill => ({
        name: skill,
        level: getSkillLevel(skill),
        category: 'Architecture',
        years: getSkillYears(skill)
      }))
    },
    management: {
      title: 'Leadership & Management',
      icon: 'Users',
      skills: managementSkills.map(skill => ({
        name: skill,
        level: getSkillLevel(skill),
        category: 'Management',
        years: getSkillYears(skill)
      }))
    }
  };
};

export const getCertifications = () => {
  return resumeJson.certificates.map((cert, index) => ({
    id: index + 1,
    name: cert.name,
    issuer: cert.issuer,
    date: cert.startDate ? new Date(cert.startDate).getFullYear().toString() : 'N/A',
    icon: 'Award',
    verified: true,
    url: cert.url
  }));
};

export const getAwards = () => {
  return resumeJson.awards || [];
};

export const getPublications = () => {
  return resumeJson.publications || [];
};

export const getVolunteerWork = () => {
  return resumeJson.volunteer || [];
};

export const getReferences = () => {
  return resumeJson.references || [];
};

function getCompanyLogo(companyName) {
  const logoMap = {
    'AHEAD': 'https://www.servicenow.com/content/dam/servicenow-assets/public/en-us/digital-graphics/ds-logos/logo-ahead.svg',
    'Amazon Web Services (AWS)': 'https://upload.wikimedia.org/wikipedia/commons/9/93/Amazon_Web_Services_Logo.svg',
    'Red Hat': 'https://upload.wikimedia.org/wikipedia/commons/d/d8/Red_Hat_logo.svg',
    'FICO': 'https://upload.wikimedia.org/wikipedia/commons/9/9a/FICO_logo.svg',
    'American Express': 'https://upload.wikimedia.org/wikipedia/commons/f/fa/American_Express_logo_%282018%29.svg',
    'VCE': 'https://upload.wikimedia.org/wikipedia/commons/thumb/a/a7/VCE_%28company%29_logo.svg/2560px-VCE_%28company%29_logo.svg.png',
    'Microsoft': 'https://upload.wikimedia.org/wikipedia/commons/9/96/Microsoft_logo_%282012%29.svg'
  };
  
  return logoMap[companyName] || 'https://via.placeholder.com/48x48/666666/ffffff?text=' + companyName.charAt(0);
}

function getSkillLevel(skillName) {
  // Assign skill levels based on experience and importance
  const highLevel = ['Amazon Web Services (AWS)', 'OpenStack', 'Kubernetes', 'Cloud Computing', 'Linux'];
  const mediumLevel = ['Docker', 'OpenShift', 'DevOps', 'Virtualization', 'Management'];
  
  if (highLevel.includes(skillName)) return 90 + Math.floor(Math.random() * 10);
  if (mediumLevel.includes(skillName)) return 80 + Math.floor(Math.random() * 10);
  return 70 + Math.floor(Math.random() * 15);
}

function getSkillYears(skillName) {
  // Estimate years based on career progression
  const seniorSkills = ['Management', 'Cloud Computing', 'Linux', 'Virtualization'];
  const midSkills = ['Kubernetes', 'Docker', 'OpenShift', 'DevOps'];
  
  if (seniorSkills.includes(skillName)) return '10+';
  if (midSkills.includes(skillName)) return '5+';
  return '3+';
}

export const getCareerStats = () => {
  const workExperience = resumeJson.work;
  const startYear = Math.min(...workExperience.map(job => new Date(job.startDate).getFullYear()));
  const currentYear = new Date().getFullYear();
  const yearsExperience = currentYear - startYear;
  
  return {
    yearsExperience,
    companiesWorked: workExperience.length,
    certifications: resumeJson.certificates.length,
    publications: resumeJson.publications?.length || 0
  };
};