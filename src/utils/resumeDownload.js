import { getResumeData } from './resumeData';

export const downloadResume = (format = 'json') => {
  const resumeData = getResumeData();
  const basicInfo = resumeData.basics;
  const fileName = `${basicInfo.name.replace(/\s+/g, '-').toLowerCase()}-resume`;

  if (format === 'json') {
    const dataStr = `data:text/json;charset=utf-8,${encodeURIComponent(
      JSON.stringify(resumeData, null, 2)
    )}`;
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute('href', dataStr);
    downloadAnchorNode.setAttribute('download', `${fileName}.json`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  } else if (format === 'md') {
    // Generate markdown content
    let markdownContent = `# ${basicInfo.name}\n\n`;
    markdownContent += `## Contact Information\n`;
    markdownContent += `- **Email:** ${basicInfo.email}\n`;
    markdownContent += `- **Phone:** ${basicInfo.phone || 'N/A'}\n`;
    markdownContent += `- **Website:** ${basicInfo.website || 'N/A'}\n`;
    markdownContent += `- **Location:** ${basicInfo.location?.city || ''}, ${basicInfo.location?.country || ''}\n\n`;

    // Add summary
    if (basicInfo.summary) {
      markdownContent += `## Summary\n${basicInfo.summary}\n\n`;
    }

    // Add work experience
    if (resumeData.work?.length > 0) {
      markdownContent += `## Work Experience\n\n`;
      resumeData.work.forEach(job => {
        markdownContent += `### ${job.position} at ${job.name}\n`;
        markdownContent += `*${job.startDate} - ${job.endDate || 'Present'}*\n`;
        markdownContent += `${job.location || 'Remote'}\n\n`;
        if (job.highlights?.length > 0) {
          job.highlights.forEach(highlight => {
            markdownContent += `- ${highlight}\n`;
          });
        }
        markdownContent += '\n';
      });
    }

    // Add education
    if (resumeData.education?.length > 0) {
      markdownContent += `## Education\n\n`;
      resumeData.education.forEach(edu => {
        markdownContent += `### ${edu.studyType} in ${edu.area}\n`;
        markdownContent += `*${edu.institution}*\n`;
        markdownContent += `${edu.startDate} - ${edu.endDate || 'Present'}\n\n`;
      });
    }

    // Add skills
    if (resumeData.skills?.length > 0) {
      markdownContent += `## Skills\n\n`;
      resumeData.skills.forEach(skill => {
        markdownContent += `- **${skill.name}:** ${skill.keywords?.join(', ')}\n`;
      });
    }

    // Create and trigger download
    const dataStr = `data:text/markdown;charset=utf-8,${encodeURIComponent(markdownContent)}`;
    const downloadAnchorNode = document.createElement('a');
    downloadAnchorNode.setAttribute('href', dataStr);
    downloadAnchorNode.setAttribute('download', `${fileName}.md`);
    document.body.appendChild(downloadAnchorNode);
    downloadAnchorNode.click();
    downloadAnchorNode.remove();
  }
};
