import React from "react";
import Routes from "./Routes";
import { StagewiseToolbar } from "@stagewise/toolbar-react";
import { ReactPlugin } from "@stagewise-plugins/react";

/**
 * The main application component.
 *
 * This component is the root of the application and is responsible for rendering
 * the main routing component. It also includes the Stagewise Toolbar, which is
 * a development tool that is only rendered in development mode.
 */
function App() {
  return (
    <>
      {/* Stagewise Toolbar - only renders in development mode */}
      {process.env.NODE_ENV === 'development' && (
        <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
      )}
      {/* The main routing component */}
      <Routes />
    </>
  );
}

export default App;