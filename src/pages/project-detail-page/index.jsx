import React, { useEffect } from 'react';
import Header from '../../components/ui/Header';
import Breadcrumb from '../../components/ui/Breadcrumb';
import NavigationIndicator from '../../components/ui/NavigationIndicator';
import ProjectHero from './components/ProjectHero';
import ProjectOverview from './components/ProjectOverview';
import TechnologyStack from './components/TechnologyStack';
import ProjectGallery from './components/ProjectGallery';
import DevelopmentProcess from './components/DevelopmentProcess';
import ProjectTestimonial from './components/ProjectTestimonial';
import RelatedProjects from './components/RelatedProjects';
import ProjectNavigation from './components/ProjectNavigation';
import SocialShare from './components/SocialShare';

/**
 * The main component for the project detail page.
 *
 * This component renders the entire project detail page, including the header,
 * breadcrumbs, navigation indicator, project hero, project overview,
 * technology stack, project gallery, development process, project testimonial,
 * related projects, social share, and project navigation.
 */
const ProjectDetailPage = () => {
  // Mock project data
  const project = {
    title: "E-Commerce Platform Redesign",
    description: "A comprehensive redesign of a multi-vendor e-commerce platform focusing on user experience, performance optimization, and mobile-first design principles.",
    category: "Web Development",
    year: "2024",
    duration: "4 months",
    teamSize: "5",
    platform: "Web",
    heroImage: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=800&h=600&fit=crop",
    isVideo: false,
    liveUrl: "https://demo-ecommerce.example.com",
    githubUrl: "https://github.com/nicholasg-dev/ecommerce-platform",
    challenge: `The existing platform suffered from poor user experience, slow loading times, and low conversion rates. Mobile users faced particular difficulties with navigation and checkout processes, leading to high abandonment rates.`,
    solution: `Implemented a mobile-first redesign with optimized user flows, integrated modern payment systems, and built a scalable component library. Used React 18 with server-side rendering for improved performance and SEO.`,
    results: `Achieved 45% increase in conversion rates, 60% improvement in page load speeds, and 80% reduction in mobile checkout abandonment. User satisfaction scores improved from 3.2 to 4.7 out of 5.`,
    metrics: [
      { value: "45%", label: "Conversion Increase" },
      { value: "60%", label: "Speed Improvement" },
      { value: "80%", label: "Reduced Abandonment" },
      { value: "4.7/5", label: "User Satisfaction" }
    ]
  };

  const technologies = [
    { name: "React", logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg", category: "frontend", description: "Component-based UI library for building interactive interfaces" },
    { name: "TypeScript", logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg", category: "frontend", description: "Type-safe JavaScript for better development experience" },
    { name: "Next.js", logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg", category: "frontend", description: "React framework with SSR and optimization features" },
    { name: "Tailwind CSS", logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/tailwindcss/tailwindcss-plain.svg", category: "frontend", description: "Utility-first CSS framework for rapid UI development" },
    { name: "Node.js", logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg", category: "backend", description: "JavaScript runtime for server-side development" },
    { name: "Express", logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/express/express-original.svg", category: "backend", description: "Minimal web framework for Node.js applications" },
    { name: "MongoDB", logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg", category: "database", description: "NoSQL database for flexible data storage" },
    { name: "Redis", logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/redis/redis-original.svg", category: "database", description: "In-memory data store for caching and sessions" },
    { name: "Docker", logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/docker/docker-original.svg", category: "tools", description: "Containerization platform for consistent deployments" },
    { name: "AWS", logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/amazonwebservices/amazonwebservices-original.svg", category: "tools", description: "Cloud platform for hosting and scaling applications" }
  ];

  const galleryImages = [
    {
      url: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop",
      title: "Homepage Design",
      caption: "Clean and modern homepage with featured products and categories"
    },
    {
      url: "https://images.unsplash.com/photo-**********-824ae1b704d3?w=600&h=400&fit=crop",
      title: "Product Listing",
      caption: "Responsive product grid with advanced filtering options"
    },
    {
      url: "https://images.unsplash.com/photo-1556742111-a301076d9d18?w=600&h=400&fit=crop",
      title: "Product Details",
      caption: "Detailed product view with image gallery and specifications"
    },
    {
      url: "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600&h=400&fit=crop",
      title: "Shopping Cart",
      caption: "Streamlined cart experience with real-time updates"
    },
    {
      url: "https://images.unsplash.com/photo-**********-824ae1b704d3?w=600&h=400&fit=crop",
      title: "Checkout Process",
      caption: "Simplified multi-step checkout with progress indicators"
    },
    {
      url: "https://images.unsplash.com/photo-1556742111-a301076d9d18?w=600&h=400&fit=crop",
      title: "Mobile Interface",
      caption: "Mobile-optimized design with touch-friendly interactions"
    }
  ];

  const processSteps = [
    {
      title: "Research & Discovery",
      summary: "Understanding user needs and business requirements",
      duration: "2 weeks",
      timeline: "Week 1-2",
      details: `Conducted comprehensive user research including surveys, interviews, and usability testing of the existing platform. Analyzed competitor solutions and identified key pain points in the current user journey. Created detailed user personas and mapped out the customer journey from discovery to purchase.`,
      decisions: [
        "Focus on mobile-first design approach based on 70% mobile traffic",
        "Prioritize checkout optimization due to high abandonment rates",
        "Implement progressive web app features for better mobile experience"
      ],
      tools: ["Figma", "Miro", "Google Analytics", "Hotjar"],
      images: [
        {
          url: "https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?w=400&h=300&fit=crop",
          caption: "User research session and persona development"
        },
        {
          url: "https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?w=400&h=300&fit=crop",
          caption: "Competitor analysis and market research"
        }
      ]
    },
    {
      title: "Design & Prototyping",
      summary: "Creating wireframes, mockups, and interactive prototypes",
      duration: "3 weeks",
      timeline: "Week 3-5",
      details: `Developed low-fidelity wireframes focusing on information architecture and user flow optimization. Created high-fidelity mockups with the new visual design system, ensuring consistency across all pages. Built interactive prototypes for user testing and stakeholder feedback.`,
      decisions: [
        "Adopt card-based layout for better content organization",
        "Implement sticky navigation for improved accessibility",
        "Use progressive disclosure to reduce cognitive load"
      ],
      tools: ["Figma", "Principle", "InVision", "Sketch"],
      images: [
        {
          url: "https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?w=400&h=300&fit=crop",
          caption: "Wireframe sketches and user flow diagrams"
        }
      ]
    },
    {
      title: "Frontend Development",
      summary: "Building responsive components and user interfaces",
      duration: "6 weeks",
      timeline: "Week 6-11",
      details: `Implemented the design system using React and TypeScript, creating reusable components for consistent UI patterns. Integrated with backend APIs and implemented state management for complex user interactions. Optimized for performance with code splitting and lazy loading.`,
      decisions: [
        "Use React Query for efficient data fetching and caching",
        "Implement virtual scrolling for large product lists",
        "Add skeleton loading states for better perceived performance"
      ],
      tools: ["React", "TypeScript", "Tailwind CSS", "Vite"],
      images: [
        {
          url: "https://images.pexels.com/photos/3184360/pexels-photo-3184360.jpeg?w=400&h=300&fit=crop",
          caption: "Component development and testing"
        }
      ]
    },
    {
      title: "Testing & Optimization",
      summary: "Quality assurance and performance optimization",
      duration: "2 weeks",
      timeline: "Week 12-13",
      details: `Conducted comprehensive testing including unit tests, integration tests, and end-to-end testing. Performed accessibility audits and optimized for SEO. Load tested the application and implemented performance improvements.`,
      decisions: [
        "Implement automated testing pipeline with Jest and Cypress",
        "Add comprehensive error tracking with Sentry",
        "Optimize images with next-gen formats and lazy loading"
      ],
      tools: ["Jest", "Cypress", "Lighthouse", "WebPageTest"],
      images: []
    },
    {
      title: "Launch & Monitoring",
      summary: "Deployment and post-launch performance monitoring",
      duration: "1 week",
      timeline: "Week 14-15",
      details: `Deployed the application using CI/CD pipeline with comprehensive monitoring and alerting. Conducted A/B tests to validate design decisions and monitored key performance metrics. Gathered user feedback and planned future iterations.`,
      decisions: [
        "Implement gradual rollout to minimize risk",
        "Set up comprehensive analytics and monitoring",
        "Create feedback collection system for continuous improvement"
      ],
      tools: ["AWS", "Docker", "GitHub Actions", "DataDog"],
      images: []
    }
  ];

  const testimonial = {
    quote: "The team delivered an exceptional redesign that exceeded our expectations. The new platform not only looks amazing but has significantly improved our business metrics. Our customers love the new experience, and we've seen remarkable improvements in conversion rates.",
    name: "Sarah Johnson",
    role: "Head of Digital Marketing",
    company: "TechCommerce Inc.",
    avatar: "https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100&h=100&fit=crop&crop=face"
  };

  const impactMetrics = [
    {
      icon: "TrendingUp",
      value: "45%",
      label: "Conversion Rate",
      description: "Increase in overall conversion"
    },
    {
      icon: "Zap",
      value: "2.1s",
      label: "Load Time",
      description: "Average page load speed"
    },
    {
      icon: "Users",
      value: "89%",
      label: "User Satisfaction",
      description: "Positive feedback score"
    },
    {
      icon: "ShoppingCart",
      value: "67%",
      label: "Cart Completion",
      description: "Checkout completion rate"
    },
    {
      icon: "Smartphone",
      value: "92%",
      label: "Mobile Usage",
      description: "Mobile user engagement"
    },
    {
      icon: "Star",
      value: "4.8/5",
      label: "App Store Rating",
      description: "User rating improvement"
    }
  ];

  const relatedProjects = [
    {
      title: "SaaS Dashboard Redesign",
      description: "Complete overhaul of a B2B SaaS platform dashboard with focus on data visualization and user workflow optimization.",
      category: "UI/UX Design",
      year: "2024",
      image: "https://images.unsplash.com/photo-**********-bebda4e38f71?w=400&h=300&fit=crop",
      technologies: ["React", "D3.js", "TypeScript", "Material-UI"],
      liveUrl: "https://demo-saas.example.com",
      githubUrl: "https://github.com/nicholasg-dev/saas-dashboard"
    },
    {
      title: "Mobile Banking App",
      description: "Native mobile application for digital banking with advanced security features and intuitive user experience.",
      category: "Mobile Development",
      year: "2023",
      image: "https://images.unsplash.com/photo-**********-824ae1b704d3?w=400&h=300&fit=crop",
      technologies: ["React Native", "Node.js", "MongoDB", "AWS"],
      liveUrl: "https://demo-banking.example.com",
      githubUrl: "https://github.com/nicholasg-dev/banking-app"
    },
    {
      title: "Learning Management System",
      description: "Comprehensive LMS platform for online education with video streaming, assessments, and progress tracking.",
      category: "Web Development",
      year: "2023",
      image: "https://images.unsplash.com/photo-*************-66273c2fd55f?w=400&h=300&fit=crop",
      technologies: ["Vue.js", "Laravel", "MySQL", "Redis"],
      liveUrl: "https://demo-lms.example.com",
      githubUrl: "https://github.com/nicholasg-dev/lms-platform"
    }
  ];

  const navigationProjects = {
    previous: {
      title: "SaaS Dashboard Redesign",
      slug: "saas-dashboard-redesign"
    },
    next: {
      title: "Mobile Banking App",
      slug: "mobile-banking-app"
    }
  };

  const sections = [
    { id: 'hero', label: 'Overview' },
    { id: 'project-overview', label: 'Project Details' },
    { id: 'technology-stack', label: 'Technologies' },
    { id: 'gallery', label: 'Gallery' },
    { id: 'process', label: 'Process' },
    { id: 'testimonial', label: 'Testimonial' },
    { id: 'related', label: 'Related Projects' }
  ];

  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <NavigationIndicator sections={sections} />
      
      <main className="pt-16 lg:pt-20">
        <div className="max-w-7xl mx-auto px-6 lg:px-12 py-6">
          <Breadcrumb />
        </div>

        <div id="hero">
          <ProjectHero project={project} />
        </div>

        <div id="project-overview">
          <ProjectOverview project={project} />
        </div>

        <div id="technology-stack">
          <TechnologyStack technologies={technologies} />
        </div>

        <div id="gallery">
          <ProjectGallery images={galleryImages} />
        </div>

        <div id="process">
          <DevelopmentProcess processSteps={processSteps} />
        </div>

        <div id="testimonial">
          <ProjectTestimonial testimonial={testimonial} metrics={impactMetrics} />
        </div>

        <div id="related">
          <RelatedProjects projects={relatedProjects} />
        </div>

        <SocialShare project={project} />
        <ProjectNavigation 
          previousProject={navigationProjects.previous} 
          nextProject={navigationProjects.next} 
        />
      </main>
    </div>
  );
};

export default ProjectDetailPage;