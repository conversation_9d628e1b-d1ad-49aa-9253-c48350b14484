import React from 'react';
import { Link } from 'react-router-dom';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { label: 'Home', path: '/portfolio-homepage' },
    { label: 'About', path: '/about-page' },
    { label: 'Projects', path: '/projects-portfolio' },
    
    { label: 'Contact', path: '/contact-page' }
  ];

  const socialLinks = [
    { name: 'GitHub', icon: 'Github', url: 'https://github.com/nicholasg-dev' },
    { name: 'LinkedIn', icon: 'Linkedin', url: 'https://www.linkedin.com/in/ngerasimatos/' }
  ];

  const services = [
    'Web Development',
    'Mobile App Development',
    'UI/UX Design',
    'API Development',
    'Technical Consulting',
    'Code Review'
  ];

  return (
    <footer className="bg-primary text-primary-foreground">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-6 lg:px-12 py-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <p className="text-primary-foreground/80 mb-6 leading-relaxed">
              Delivering innovative cloud and platform engineering solutions with expertise in enterprise architecture.
            </p>
            
            <div className="flex space-x-3">
              {socialLinks.map((social) => (
                <a
                  key={social.name}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="w-10 h-10 bg-primary-foreground/10 hover:bg-accent rounded-full flex items-center justify-center text-primary-foreground hover:text-accent-foreground transition-smooth"
                  aria-label={`Follow on ${social.name}`}
                >
                  <Icon name={social.icon} size={18} />
                </a>
              ))}
            </div>
          </div>
          
          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-6">Quick Links</h4>
            <ul className="space-y-3">
              {quickLinks.map((link) => (
                <li key={link.path}>
                  <Link
                    to={link.path}
                    className="text-primary-foreground/80 hover:text-accent transition-smooth flex items-center group"
                  >
                    <Icon name="ArrowRight" size={14} className="mr-2 opacity-0 group-hover:opacity-100 transition-smooth" />
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
      
      {/* Bottom Bar */}
      <div className="border-t border-primary-foreground/10">
        <div className="max-w-7xl mx-auto px-6 lg:px-12 py-6">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="text-primary-foreground/60 text-sm mb-4 md:mb-0">
              © {currentYear} Nicholas Gerasimatos. All rights reserved. Built with React & Tailwind CSS.
            </div>
            
            <div className="flex items-center text-primary-foreground/60">
              <Icon name="Heart" size={14} className="mr-1 text-accent" />
              Made with passion
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;