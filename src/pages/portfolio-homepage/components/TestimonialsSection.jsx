import React, { useState, useEffect } from 'react';
import Image from '../../../components/AppImage';
import Icon from '../../../components/AppIcon';
import { getReferences } from '../../../utils/resumeData';

const TestimonialsSection = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);
  const [testimonials, setTestimonials] = useState([]);

  useEffect(() => {
    // Get references from resume data
    const references = getReferences();
    
    // Map references to testimonial format
    const formattedTestimonials = references.map((ref, index) => ({
      id: index + 1,
      name: ref.name || 'Professional Contact',
      role: ref.position || '',
      company: ref.organization || '',
      avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(ref.name || 'Professional')}&background=random`,
      content: ref.reference,
      rating: 5,
      project: 'Professional Collaboration'
    }));
    
    setTestimonials(formattedTestimonials);
  }, []);

  useEffect(() => {
    if (testimonials.length > 0) {
      const interval = setInterval(() => {
        setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [testimonials.length]);

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const goToTestimonial = (index) => {
    setCurrentTestimonial(index);
  };

  return (
    <section id="testimonials" className="py-20 bg-background">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-16">
          <span className="text-accent font-semibold text-sm uppercase tracking-wide">Client Testimonials</span>
          <h2 className="text-3xl md:text-4xl font-bold text-primary mt-2 mb-4">
            What Clients Say About My Work
          </h2>
        </div>
        
        <div className="relative max-w-4xl mx-auto">
          {/* Main Testimonial */}
          <div className="bg-surface rounded-lg p-8 md:p-12 card-shadow relative overflow-hidden">
            {/* Quote Icon */}
            <div className="absolute top-6 right-6 text-accent/20">
              <Icon name="Quote" size={48} />
            </div>
            
            <div className="relative z-10">

              
              {/* Content */}
              {testimonials.length > 0 && testimonials[currentTestimonial] && (
                <blockquote className="text-lg md:text-xl text-text-primary leading-relaxed mb-8 italic">
                  "{testimonials[currentTestimonial].content || 'Great work experience!'}"
                </blockquote>
              )}
              
              {/* Author Info */}
              {testimonials.length > 0 && testimonials[currentTestimonial] && (
                <div className="flex items-center">
                  <div className="w-16 h-16 rounded-full overflow-hidden mr-4 bg-accent/10 flex items-center justify-center">
                    {testimonials[currentTestimonial]?.avatar ? (
                      <Image
                        src={testimonials[currentTestimonial].avatar}
                        alt={testimonials[currentTestimonial].name || 'Testimonial'}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <Icon name="User" size={24} className="text-accent" />
                    )}
                  </div>
                  <div>
                    <div className="font-semibold text-primary text-lg">
                      {testimonials[currentTestimonial]?.name || 'Professional Contact'}
                    </div>
                    {testimonials[currentTestimonial]?.company && (
                      <div className="text-text-secondary">
                        {testimonials[currentTestimonial].company}
                      </div>
                    )}
                    {testimonials[currentTestimonial]?.role && (
                      <div className="text-text-secondary text-sm">
                        {testimonials[currentTestimonial].role}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {/* Navigation Controls */}
          <div className="flex items-center justify-between mt-8">
            {/* Previous Button */}
            <button
              onClick={prevTestimonial}
              className="w-12 h-12 bg-surface hover:bg-accent/10 rounded-full flex items-center justify-center text-text-secondary hover:text-accent transition-smooth"
              aria-label="Previous testimonial"
            >
              <Icon name="ChevronLeft" size={20} />
            </button>
            
            {/* Dots Indicator */}
            <div className="flex space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => goToTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-smooth ${
                    index === currentTestimonial
                      ? 'bg-accent' :'bg-border hover:bg-accent/50'
                  }`}
                  aria-label={`Go to testimonial ${index + 1}`}
                />
              ))}
            </div>
            
            {/* Next Button */}
            <button
              onClick={nextTestimonial}
              className="w-12 h-12 bg-surface hover:bg-accent/10 rounded-full flex items-center justify-center text-text-secondary hover:text-accent transition-smooth"
              aria-label="Next testimonial"
            >
              <Icon name="ChevronRight" size={20} />
            </button>
          </div>
        </div>
        
        {/* All Testimonials Preview */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mt-16">
          {testimonials.map((testimonial, index) => (
            <div
              key={testimonial?.id || index}
              onClick={() => goToTestimonial(index)}
              className={`bg-surface rounded-lg p-4 cursor-pointer transition-smooth ${
                index === currentTestimonial
                  ? 'ring-2 ring-accent bg-accent/5' :'hover:bg-background'
              }`}
            >
              <div className="flex items-center mb-3">
                <div className="w-10 h-10 rounded-full overflow-hidden mr-3">
                  {testimonial?.avatar ? (
                    <Image
                      src={testimonial.avatar}
                      alt={testimonial?.name || 'Testimonial'}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <Icon name="User" size={24} className="text-accent" />
                  )}
                </div>
                <div>
                  <div className="font-medium text-primary text-sm">{testimonial?.name || 'Professional Contact'}</div>
                  <div className="text-text-secondary text-xs">{testimonial?.company || ''}</div>
                </div>
              </div>
              <p className="text-text-secondary text-sm line-clamp-3">
                "{testimonial?.content ? testimonial.content.substring(0, 100) : 'Great work experience!'}..."
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;