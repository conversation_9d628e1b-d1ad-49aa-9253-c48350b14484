import React from 'react';
import { Link } from 'react-router-dom';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';

const FeaturedProjects = () => {
  const projects = [
    {
      id: 1,
      title: 'Enterprise Cloud-Native Platform',
      description: 'A comprehensive cloud-native platform built on OpenShift and AWS, enabling enterprises to modernize applications with containerization and microservices architecture.',
      image: '/Cloud Native Platform.jpeg',
      technologies: ['OpenShift', 'AWS', 'Kubernetes', 'ArgoCD', 'Istio', 'Terraform'],
      category: 'Cloud Architecture',
      liveUrl: '#',
      githubUrl: '#'
    },
    {
      id: 2,
      title: 'AI/ML Model Serving Platform',
      description: 'A production-grade platform for deploying, managing, and monitoring machine learning models at scale with MLOps best practices.',
      image: 'https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop',
      technologies: ['Kubeflow', 'AWS Bedrock', 'MLflow', 'KServe', 'TensorFlow', 'PyTorch'],
      category: 'AI/ML',
      liveUrl: '#',
      githubUrl: '#'
    },
    {
      id: 3,
      title: 'Hybrid Cloud Data Fabric',
      description: 'A unified data access layer enabling seamless data movement and analytics across on-premises and multi-cloud environments.',
      image: '/Hybrid Cloud Data Fabric.jpeg',
      technologies: ['Red Hat OpenShift Data Science', 'Apache Kafka', 'Apache Spark', 'Presto', 'AWS S3'],
      category: 'Data Architecture',
      liveUrl: '#',
      githubUrl: '#'
    },
    {
      id: 4,
      title: 'Zero-Trust Security Framework',
      description: 'A comprehensive zero-trust security implementation for cloud-native applications with continuous verification and least-privilege access.',
      image: '/public/key and lock.jpeg',
      technologies: ['Istio', 'SPIFFE/SPIRE', 'Open Policy Agent', 'Vault', 'Keycloak'],
      category: 'Security',
      liveUrl: '#',
      githubUrl: '#'
    }
  ];

  return (
    <section id="featured-projects" className="py-20 bg-surface">
      <div className="max-w-7xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-16">
          <span className="text-accent font-semibold text-sm uppercase tracking-wide">Featured Work</span>
          <h2 className="text-3xl md:text-4xl font-bold text-primary mt-2 mb-4">
            Projects That Define My Expertise
          </h2>
          <p className="text-text-secondary text-lg max-w-2xl mx-auto">
            A showcase of my best work, demonstrating technical skills and creative problem-solving
          </p>
        </div>
        
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {projects.map((project, index) => (
            <div 
              key={project.id}
              className="h-full flex flex-col bg-background rounded-lg overflow-hidden card-shadow hover:shadow-interactive transition-smooth group"
            >
              <div className="relative overflow-hidden flex-shrink-0">
                <Image
                  src={project.image}
                  alt={project.title}
                  className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                />
                <div className="absolute top-4 left-4">
                  <span className="px-3 py-1 bg-accent text-accent-foreground text-xs font-medium rounded-full">
                    {project.category}
                  </span>
                </div>
                <div className="absolute top-4 right-4 flex space-x-2">
                  <a
                    href={project.liveUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-8 h-8 bg-background/90 backdrop-blur-sm rounded-full flex items-center justify-center text-text-primary hover:bg-accent hover:text-accent-foreground transition-smooth"
                    aria-label="View live demo"
                  >
                    <Icon name="ExternalLink" size={14} />
                  </a>
                  <a
                    href={project.githubUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="w-8 h-8 bg-background/90 backdrop-blur-sm rounded-full flex items-center justify-center text-text-primary hover:bg-accent hover:text-accent-foreground transition-smooth"
                    aria-label="View source code"
                  >
                    <Icon name="Github" size={14} />
                  </a>
                </div>
              </div>
              
              <div className="flex-1 flex flex-col p-6">
                <h3 className="text-xl font-semibold text-primary mb-3 group-hover:text-accent transition-smooth">
                  {project.title}
                </h3>
                
                <p className="text-text-secondary mb-4 leading-relaxed">
                  {project.description}
                </p>
                
                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.map((tech, techIndex) => (
                    <span 
                      key={techIndex}
                      className="px-3 py-1 bg-surface text-text-secondary text-xs rounded-full border border-border"
                    >
                      {tech}
                    </span>
                  ))}
                </div>
                
                <div className="mt-auto pt-4">
                  <Link to="/project-detail-page" state={{ project }}>
                    <Button 
                      variant="ghost"
                      size="sm"
                      iconName="ArrowRight"
                      iconPosition="right"
                    >
                      View Details
                    </Button>
                  </Link>
                  

                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="text-center">
          <Link to="/projects-portfolio">
            <Button 
              variant="primary"
              size="lg"
              iconName="FolderOpen"
              iconPosition="right"
            >
              View More Projects
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProjects;