import React, { useEffect } from 'react';
import Header from '../../components/ui/Header';
import NavigationIndicator from '../../components/ui/NavigationIndicator';
import HeroSection from './components/HeroSection';
import AboutPreview from './components/AboutPreview';
import SkillsSection from './components/SkillsSection';
import FeaturedProjects from './components/FeaturedProjects';
import TestimonialsSection from './components/TestimonialsSection';
import Footer from './components/Footer';

/**
 * The main component for the portfolio homepage.
 *
 * This component renders the entire homepage, including the header, navigation,
 * hero section, about preview, skills, featured projects, testimonials, and footer.
 * It also handles smooth scrolling and sets the page title and meta description.
 */
const PortfolioHomepage = () => {
  // Define the sections for the navigation indicator
  const sections = [
    { id: 'hero', label: 'Home' },
    { id: 'about-preview', label: 'About' },
    { id: 'featured-projects', label: 'Projects' },
    { id: 'contact', label: 'Contact' }
  ];

  useEffect(() => {
    // Smooth scroll behavior for anchor links
    const handleSmoothScroll = (e) => {
      const target = e.target.closest('a[href^="#"]');
      if (target) {
        e.preventDefault();
        const targetId = target.getAttribute('href').substring(1);
        const targetElement = document.getElementById(targetId);
        
        if (targetElement) {
          const headerOffset = 100;
          const elementPosition = targetElement.getBoundingClientRect().top;
          const offsetPosition = elementPosition + window.pageYOffset - headerOffset;

          window.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          });
        }
      }
    };

    document.addEventListener('click', handleSmoothScroll);
    return () => document.removeEventListener('click', handleSmoothScroll);
  }, []);

  useEffect(() => {
    // Set page title and meta description
    document.title = 'Nicholas Gerasimatos - Full Stack Developer | Nicholas Gerasimatos';
    
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute('content', 
        'Full Stack Developer specializing in React, Node.js, and modern web technologies. View my portfolio of innovative web applications and get in touch for your next project.'
      );
    }
  }, []);

  return (
    <div className="min-h-screen bg-background">
      {/* Render the header */}
      <Header />
      {/* Render the navigation indicator */}
      <NavigationIndicator sections={sections} />
      
      {/* Render the main content of the page */}
      <main>
        <HeroSection />
        <AboutPreview />
        <SkillsSection />
        <FeaturedProjects />
        <TestimonialsSection />
      </main>
      
      {/* Render the footer */}
      <Footer />
    </div>
  );
};

export default PortfolioHomepage;