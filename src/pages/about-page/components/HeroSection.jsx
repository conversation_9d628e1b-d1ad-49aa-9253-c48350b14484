import React, { useState, useRef, useEffect } from 'react';
import Image from '../../../components/AppImage';
import Button from '../../../components/ui/Button';
import Icon from '../../../components/AppIcon';
import { getBasicInfo, getCareerStats } from '../../../utils/resumeData';
import { downloadResume } from '../../../utils/resumeDownload';

const HeroSection = () => {
  const basicInfo = getBasicInfo();
  const careerStats = getCareerStats();

  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const menuRef = useRef(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setIsMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleDownloadClick = (format) => {
    downloadResume(format);
    setIsMenuOpen(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <section id="hero" className="py-20 md:py-32 flex items-center justify-center bg-gradient-to-br from-background via-surface to-background relative overflow-hidden">
      {/* Background Pattern - Smaller and more subtle */}
      <div className="absolute inset-0 opacity-3">
        <div className="absolute top-1/4 left-1/4 w-24 h-24 bg-accent rounded-full blur-2xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-32 h-32 bg-primary rounded-full blur-2xl"></div>
      </div>
      <div className="max-w-7xl mx-auto px-6 lg:px-12 py-20 relative z-10">
        <div className="items-center">
          {/* Full Width Text Content */}
          <div>
            <div className="space-y-6">
              <div>
                <h1 className="text-3xl lg:text-4xl xl:text-5xl font-bold text-primary mb-4">
                  About Me
                </h1>
                <p className="text-lg lg:text-xl text-text-secondary leading-relaxed max-w-full">
                  {basicInfo.summary}
                </p>
              </div>
              
              <div className="flex flex-wrap gap-4 relative" ref={menuRef}>
                <Button 
                  variant="primary" 
                  iconName="Download" 
                  iconPosition="left"
                  onClick={toggleMenu}
                  className="relative"
                >
                  Download Resume
                  <Icon 
                    name={isMenuOpen ? "ChevronUp" : "ChevronDown"} 
                    className="ml-2 transition-transform"
                    size={16}
                  />
                </Button>
                
                {isMenuOpen && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-surface rounded-md shadow-lg z-50 border border-border">
                    <div className="py-1">
                      <button
                        onClick={() => handleDownloadClick('json')}
                        className="w-full text-left px-4 py-2 text-sm text-text hover:bg-background hover:text-accent flex items-center"
                      >
                        <Icon name="Code" className="mr-2" size={16} />
                        Download as JSON
                      </button>
                      <button
                        onClick={() => handleDownloadClick('md')}
                        className="w-full text-left px-4 py-2 text-sm text-text hover:bg-background hover:text-accent flex items-center"
                      >
                        <Icon name="FileText" className="mr-2" size={16} />
                        Download as Markdown
                      </button>
                    </div>
                  </div>
                )}
              </div>

              {/* Quick Stats */}
              <div className="grid grid-cols-3 gap-6 pt-8 border-t border-border">
                <div className="text-center">
                  <div className="text-2xl lg:text-3xl font-bold text-accent">{careerStats.yearsExperience}+</div>
                  <div className="text-sm text-text-secondary">Years Experience</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl lg:text-3xl font-bold text-accent">{careerStats.companiesWorked}</div>
                  <div className="text-sm text-text-secondary">Companies</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl lg:text-3xl font-bold text-accent">{careerStats.certifications}+</div>
                  <div className="text-sm text-text-secondary">Certifications</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="flex flex-col items-center text-text-secondary">
          <span className="text-sm mb-2">Scroll to explore</span>
          <Icon name="ChevronDown" size={20} />
        </div>
      </div>
    </section>
  );
};

export default HeroSection;