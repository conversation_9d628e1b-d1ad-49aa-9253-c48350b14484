import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Image from '../../../components/AppImage';
import { getWorkExperience } from '../../../utils/resumeData';
import { format } from 'date-fns';
import { motion } from 'framer-motion';

const TimelineSection = () => {
  const [expandedEntry, setExpandedEntry] = useState(null);
  const timelineData = getWorkExperience();

  const toggleEntry = (entryId) => {
    setExpandedEntry(prev => prev === entryId ? null : entryId);
  };

  const formatDateRange = (startDate, endDate) => {
    const start = startDate ? new Date(startDate) : null;
    const end = endDate ? new Date(endDate) : null;

    const formatDate = (date) => {
      if (!date) return 'Present';
      return format(date, 'MMM yyyy');
    };

    const startFormatted = formatDate(start);
    const endFormatted = formatDate(end);

    return `${startFormatted} - ${endFormatted}`;
  };

  const getCompanyLogo = (companyName) => {
    const logoMap = {
      'AHEAD': 'https://www.servicenow.com/content/dam/servicenow-assets/public/en-us/digital-graphics/ds-logos/logo-ahead.svg',
      'Amazon Web Services (AWS)': 'https://upload.wikimedia.org/wikipedia/commons/9/93/Amazon_Web_Services_Logo.svg',
      'Red Hat': 'https://upload.wikimedia.org/wikipedia/commons/d/d8/Red_Hat_logo.svg',
      'FICO': 'https://upload.wikimedia.org/wikipedia/commons/9/9a/FICO_logo.svg',
      'American Express': 'https://upload.wikimedia.org/wikipedia/commons/f/fa/American_Express_logo_%282018%29.svg',
      'VCE': 'https://www.logo.wine/a/logo/VCE_(company)/VCE_(company)-Logo.wine.svg',
      'Microsoft': 'https://upload.wikimedia.org/wikipedia/commons/9/96/Microsoft_logo_%282012%29.svg'
    };
    return logoMap[companyName] || '/images/company-placeholder.png';
  };

  const getCompanyUrl = (companyName) => {
    const urlMap = {
      'AHEAD': 'https://www.thinkahead.com',
      'Amazon Web Services (AWS)': 'https://aws.amazon.com',
      'Red Hat': 'https://www.redhat.com',
      'FICO': 'https://www.fico.com',
      'American Express': 'https://www.americanexpress.com',
      'VCE': 'https://www.delltechnologies.com',
      'Microsoft': 'https://www.microsoft.com'
    };
    return urlMap[companyName] || '#';
  };

  return (
    <section id="experience" className="py-16 lg:py-20 bg-background">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <h2 className="text-3xl md:text-4xl font-bold text-primary mb-4">
            Work Experience
          </h2>
          <p className="text-lg text-text-secondary max-w-2xl mx-auto">
            My professional journey through leading technology companies
          </p>
        </motion.div>

        <div className="space-y-8">
          {timelineData.map((entry, index) => {
            const companyUrl = getCompanyUrl(entry.company);
            const logoUrl = getCompanyLogo(entry.company);

            return (
              <motion.div
                key={entry.id}
                className="relative"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="bg-surface border border-border rounded-xl p-6 hover:shadow-lg transition-all duration-300 hover:border-accent/30">
                  <div className="flex items-start gap-4 mb-4">
                    <div className="w-16 h-16 rounded-lg overflow-hidden border border-border bg-white p-2 flex-shrink-0">
                      <Image
                        src={logoUrl}
                        alt={`${entry.company} logo`}
                        className="w-full h-full object-contain"
                        width={48}
                        height={48}
                      />
                    </div>
                    <div className="flex-1 min-w-0">
                      <h3 className="text-xl font-semibold text-primary mb-1">
                        {entry.position}
                      </h3>
                      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                        <a
                          href={companyUrl}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-accent hover:text-accent/80 font-medium flex items-center w-fit"
                        >
                          {entry.company}
                          <Icon name="ExternalLink" size={14} className="ml-1.5" />
                        </a>
                        <div className="text-sm text-text-secondary bg-accent/5 px-3 py-1 rounded-full border border-accent/10 w-fit">
                          {formatDateRange(entry.startDate, entry.endDate)}
                          {!entry.endDate && (
                            <span className="ml-2 inline-flex h-2 w-2 rounded-full bg-accent animate-pulse" />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <p className="text-text-secondary leading-relaxed">
                      {entry.summary}
                    </p>
                  </div>

                  <div>
                    <div className="mb-4 mt-4 pt-4 border-t border-border">
                      <h4 className="text-sm font-semibold text-primary mb-3">Key Achievements</h4>
                      <ul className="space-y-2">
                        {entry.highlights.slice(0, 3).map((highlight, i) => (
                          <li key={i} className="flex items-start gap-2">
                            <Icon name="CheckCircle" size={16} className="text-accent mt-0.5 flex-shrink-0" />
                            <span className="text-text-secondary text-sm">{highlight}</span>
                          </li>
                        ))}
                      </ul>
                      {entry.highlights.length > 3 && (
                        <button
                          onClick={() => toggleEntry(entry.id)}
                          className="mt-3 text-sm text-accent hover:text-accent/80 font-medium flex items-center gap-1"
                        >
                          {expandedEntry === entry.id ? 'Show Less' : `View ${entry.highlights.length - 3} More`}
                          <Icon
                            name={expandedEntry === entry.id ? "ChevronUp" : "ChevronDown"}
                            size={14}
                          />
                        </button>
                      )}
                    </div>
                  </div>

                  {expandedEntry === entry.id && entry.highlights && entry.highlights.length > 3 && (
                    <motion.ul
                      className="space-y-2"
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                    >
                      {entry.highlights.slice(3).map((highlight, i) => (
                        <li key={i + 3} className="flex items-start gap-2">
                          <Icon name="CheckCircle" size={16} className="text-accent mt-0.5 flex-shrink-0" />
                          <span className="text-text-secondary text-sm">{highlight}</span>
                        </li>
                      ))}
                    </motion.ul>
                  )}
                </div>
              </motion.div>
            );
          })}
        </div>
      </div>
    </section>
  );
};

export default TimelineSection;
