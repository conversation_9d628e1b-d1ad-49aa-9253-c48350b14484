import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import { getSkills, getCertifications } from '../../../utils/resumeData';

const SkillsSection = () => {
  const [activeCategory, setActiveCategory] = useState('technical');

  const skillCategories = getSkills();
  const certifications = getCertifications();
  const skillCategoriesArray = Object.entries(skillCategories).map(([id, category]) => ({
    id,
    ...category
  }));

  // Simplified skill display - no levels or percentages

  return (
    <section id="skills" className="py-16 lg:py-20 bg-background">
      <div className="max-w-6xl mx-auto px-6 lg:px-12">
        <div className="text-center mb-8">
          <h2 className="text-2xl lg:text-3xl font-bold text-primary mb-2">
            Skills & Expertise
          </h2>
          <p className="text-base text-text-secondary max-w-2xl mx-auto">
            A comprehensive overview of my technical abilities and professional competencies.
          </p>
        </div>

        {/* Category Tabs - More compact */}
        <div className="flex flex-wrap justify-center gap-2 mb-8">
          {Object.entries(skillCategories).map(([id, category]) => (
            <button
              key={id}
              onClick={() => setActiveCategory(id)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg text-sm font-medium transition-smooth ${
                activeCategory === id
                  ? 'bg-accent text-accent-foreground'
                  : 'bg-surface text-text-secondary hover:text-text-primary hover:bg-background border border-border'
              }`}
            >
              <Icon name={category.icon} size={16} />
              <span>{category.title}</span>
            </button>
          ))}
        </div>

        {/* Skills Grid - More compact layout */}
        <div className="mb-16">
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-3">
            {skillCategories[activeCategory].skills.map((skill, index) => (
              <div
                key={index}
                className="bg-surface rounded-lg p-3 border border-border hover:shadow-card transition-smooth flex items-center justify-center text-center h-full"
              >
                <span className="font-medium text-sm text-text-primary">{skill.name}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Certifications */}
        <div className="bg-surface rounded-2xl p-6 border border-border">
          <h3 className="text-lg font-semibold text-primary mb-4 text-center">
            Certifications & Achievements
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
            {certifications.map((cert, index) => (
              <div
                key={index}
                className="bg-background rounded-lg p-4 border border-border hover:shadow-card transition-smooth flex items-center justify-center min-h-[80px] text-center"
              >
                <h4 className="font-medium text-sm text-primary m-0">{cert.name}</h4>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default SkillsSection;