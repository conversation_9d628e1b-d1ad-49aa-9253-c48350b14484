import React from 'react';
import Icon from '../../../components/AppIcon';
import { getBasicInfo } from '../../../utils/resumeData';

const CallToActionSection = () => {
  const basicInfo = getBasicInfo();
  
  const socialLinks = [
    { 
      name: 'LinkedIn', 
      icon: 'Linkedin', 
      url: 'https://linkedin.com/in/yourusername', 
      color: 'text-blue-600',
      description: 'Connect professionally and view my experience'
    },
    { 
      name: 'GitHub', 
      icon: 'Github', 
      url: 'https://github.com/nicholasg-dev', 
      color: 'text-gray-800',
      description: 'Explore my open-source contributions and projects'
    }
  ];

  return (
    <section id="contact" className="py-20 lg:py-28 bg-gradient-to-br from-accent/5 to-primary/5 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-accent rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/3 w-48 h-48 bg-primary rounded-full blur-3xl"></div>
      </div>
      
      <div className="max-w-7xl mx-auto px-6 lg:px-12 relative z-10">
        {/* Section Header */}
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl lg:text-4xl font-bold text-primary mb-6">
            Let's Connect
          </h2>
          <p className="text-lg text-text-secondary leading-relaxed">
            I'm always open to new connections and opportunities. Feel free to reach out through any of the platforms below.
          </p>
        </div>

        <div className="w-full">
          <div className="bg-background rounded-2xl p-8 border border-border shadow-card">
            <h3 className="text-xl font-semibold text-primary mb-8 text-center">
              Connect With Me
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              {socialLinks.map((social, index) => (
                <a
                  key={index}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-start p-6 bg-surface rounded-xl border border-border hover:shadow-card transition-all duration-300 group hover:border-accent/30"
                >
                  <div className={`p-3 rounded-lg bg-accent/10 group-hover:bg-accent/20 transition-colors mr-4`}>
                    <Icon 
                      name={social.icon} 
                      size={24} 
                      className={`${social.color} group-hover:scale-110 transition-transform`}
                    />
                  </div>
                  <div className="flex-1">
                    <div className="font-semibold text-lg text-text-primary group-hover:text-accent transition-smooth">
                      {social.name}
                    </div>
                    <p className="text-sm text-text-secondary mt-1">
                      {social.description}
                    </p>
                  </div>
                  <Icon 
                    name="ArrowUpRight" 
                    size={20} 
                    className="ml-4 text-text-tertiary group-hover:text-accent transition-colors self-center" 
                  />
                </a>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default CallToActionSection;