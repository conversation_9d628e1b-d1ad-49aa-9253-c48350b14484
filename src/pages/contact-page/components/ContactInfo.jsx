import React from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const ContactInfo = () => {
  const contactMethods = [
    {
      icon: 'Mail',
      label: 'Email',
      value: '<EMAIL>',
      href: 'mailto:<EMAIL>',
      description: 'Best for detailed project discussions'
    },
    {
      icon: 'Linkedin',
      label: 'LinkedIn',
      value: 'in/ngerasimatos',
      href: 'https://www.linkedin.com/in/ngerasimatos/',
      description: 'Connect for professional inquiries'
    },
    {
      icon: 'Github',
      label: 'GitHub',
      value: 'nicholasg-dev',
      href: 'https://github.com/nicholasg-dev',
      description: 'View my open-source contributions'
    }
  ];

  const socialLinks = [
    {
      icon: 'Github',
      label: 'GitHub',
      href: 'https://github.com/nicholasg-dev',
      username: 'nicholasg-dev'
    },
    {
      icon: 'Linkedin',
      label: 'LinkedIn',
      href: 'https://www.linkedin.com/in/ngerasimatos/',
      username: '/in/ngerasimatos'
    }
  ];

  const stats = [
    {
      icon: 'Clock',
      label: 'Response Time',
      value: '< 24 hours',
      description: 'Average response time'
    },
    {
      icon: 'Users',
      label: 'Client Satisfaction',
      value: '98%',
      description: 'Based on 50+ projects'
    },
    {
      icon: 'Calendar',
      label: 'Availability',
      value: 'Open',
      description: 'Currently accepting new projects'
    }
  ];

  const quickActions = [
    {
      icon: 'Download',
      title: 'Download Resume',
      description: 'Get my latest resume in PDF format',
      action: 'download',
      buttonText: 'Download PDF',
      url: '/Nicholas_Gerasimatos_Resume.pdf'
    },
    {
      icon: 'Mail',
      title: 'Email Me',
      description: 'Send me a detailed message',
      action: 'email',
      buttonText: 'Send Email',
      url: 'mailto:<EMAIL>'
    },
    {
      icon: 'Linkedin',
      title: 'Connect on LinkedIn',
      description: 'Let\'s connect professionally',
      action: 'linkedin',
      buttonText: 'View Profile',
      url: 'https://linkedin.com/in/ngerasimatos'
    }
  ];

  const handleQuickAction = (action) => {
    switch (action) {
      case 'download':
        // Simulate file download
        const link = document.createElement('a');
        link.href = '/assets/resume.pdf'; // Mock file path
        link.download = 'PortfolioPro_Resume.pdf';
        link.click();
        break;
      case 'whatsapp':
        window.open('https://wa.me/15551234567?text=Hi! I found your portfolio and would like to discuss a project.', '_blank');
        break;
      default:
        break;
    }
  };

  return (
    <div className="space-y-8">
      {/* Social Links */}
      <div className="bg-background rounded-xl shadow-card p-8">
        <h2 className="text-2xl font-semibold text-primary mb-6">
          Connect on Social
        </h2>
        
        <div className="grid grid-cols-2 gap-4">
          {socialLinks.map((social, index) => (
            <a
              key={index}
              href={social.href}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center gap-3 p-4 rounded-lg border border-border hover:border-accent/30 hover:bg-accent/5 transition-smooth group"
            >
              <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center group-hover:bg-accent/20 transition-smooth">
                <Icon name={social.icon} size={18} className="text-accent" />
              </div>
              
              <div className="flex-1 min-w-0">
                <p className="font-medium text-text-primary text-sm">
                  {social.label}
                </p>
                <p className="text-xs text-text-secondary truncate">
                  {social.username}
                </p>
              </div>
            </a>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-background rounded-xl shadow-card p-8">
        <h2 className="text-2xl font-semibold text-primary mb-6">
          Quick Actions
        </h2>
        
        <div className="space-y-4">
          {quickActions.map((action, index) => (
            <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg hover:border-accent/30 hover:bg-accent/5 transition-smooth">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-accent/10 rounded-lg flex items-center justify-center">
                  <Icon name={action.icon} size={20} className="text-accent" />
                </div>
                
                <div>
                  <h3 className="font-medium text-text-primary mb-1">
                    {action.title}
                  </h3>
                  <p className="text-sm text-text-secondary">
                    {action.description}
                  </p>
                </div>
              </div>
              
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction(action.action)}
                iconName={action.icon}
                iconPosition="left"
              >
                {action.buttonText}
              </Button>
            </div>
          ))}
        </div>
      </div>

      {/* Contact Methods */}
    </div>
  );
};

export default ContactInfo;