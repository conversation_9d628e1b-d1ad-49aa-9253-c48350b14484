import React, { useState, useEffect, useMemo } from 'react';
import { Helmet } from 'react-helmet';
import Header from '../../components/ui/Header';
import Breadcrumb from '../../components/ui/Breadcrumb';
import FilterSection from './components/FilterSection';
import SearchAndSort from './components/SearchAndSort';
import ProjectGrid from './components/ProjectGrid';
import ProjectPreviewModal from './components/ProjectPreviewModal';

/**
 * The main component for the projects portfolio page.
 *
 * This component renders the entire projects portfolio, including the header,
 * breadcrumbs, filter section, search and sort functionality, project grid,
 * and a project preview modal. It also manages the state for filtering,
 * searching, and sorting the projects.
 */
const ProjectsPortfolio = () => {
  // Mock Projects Data
  const mockProjects = [
    {
      id: 1,
      title: "Enterprise Cloud-Native Platform",
      description: "A comprehensive cloud-native platform built on OpenShift and AWS, enabling enterprises to modernize applications with containerization and microservices architecture.",
      fullDescription: `Designed and implemented a scalable, multi-tenant cloud-native platform that accelerated application modernization for enterprise clients. The solution leveraged Red Hat OpenShift on AWS (ROSA) with integrated service mesh, GitOps workflows, and comprehensive observability stack.\n\nArchitectural highlights:\n• Multi-cluster architecture spanning three AWS regions with global load balancing\n• Automated cluster lifecycle management with Advanced Cluster Management\n• Self-service developer platform with namespace-as-a-service capabilities\n• Platform monitoring with Prometheus, Grafana, and OpenTelemetry\n• GitOps-driven configuration management using ArgoCD and Kustomize\n• Service mesh implementation with Istio for traffic management, security, and observability\n• Integrated DevSecOps pipelines with Tekton and Quay\n\nBusiness impact:\n• Reduced application deployment times by 75% through GitOps automation\n• Decreased operational overhead by 60% with platform automation\n• Improved developer productivity with self-service capabilities\n• Achieved 99.99% availability across multiple regions through advanced resilience patterns\n• Accelerated time-to-market for new features by 40%\n• Enabled 15+ development teams to adopt cloud-native practices\n\nThe platform included a comprehensive disaster recovery strategy with cross-region failover capabilities, immutable infrastructure patterns, and chaos engineering practices to validate resilience.`,
      image: "/Cloud Native Platform.jpeg",
      category: "Cloud Architecture",
      technologies: ["OpenShift", "AWS", "Kubernetes", "ArgoCD", "Istio", "Terraform", "Tekton", "Prometheus", "Grafana", "OpenTelemetry"],
      year: "2024",
      duration: "8 months",
      role: "Principal Architect",
      teamSize: "10 engineers",
      client: "Fortune 500 Financial Services"
    },
    {
      id: 2,
      title: "AI/ML Model Serving Platform",
      description: "A production-grade platform for deploying, managing, and monitoring machine learning models at scale with MLOps best practices.",
      fullDescription: `Developed a comprehensive MLOps platform for a major healthcare provider that enabled their data science teams to rapidly deploy and manage machine learning models in production environments. The solution integrated AWS Bedrock with open-source components to create a complete machine learning lifecycle management platform.\n\nArchitectural components:\n• Model training infrastructure with distributed training support using Kubeflow on OpenShift\n• Model registry with versioning, metadata tracking, and lineage using MLflow\n• Feature store implementation using Amazon SageMaker Feature Store\n• Inference service layer with RESTful and gRPC endpoints using KServe\n• Autoscaling inference endpoints with GPU/CPU optimization based on workload characteristics\n• A/B testing framework with statistical significance analysis\n• Canary deployment capabilities with automated rollback based on performance metrics\n• Comprehensive observability with model monitoring, drift detection, and explainability\n• Security controls including model artifacts scanning and secure inference endpoints\n\nTechnical implementation:\n• Designed event-driven architecture using AWS EventBridge to coordinate ML pipeline activities\n• Implemented GitOps workflow for model deployment with immutable artifacts\n• Created custom operators for Kubernetes to handle ML-specific resource management\n• Built specialized containers optimized for different ML frameworks (TensorFlow, PyTorch, etc.)\n• Developed model performance dashboards with real-time metrics visualization\n\nBusiness impact:\n• Reduced model deployment time from weeks to hours (95% improvement)\n• Improved model performance monitoring, reducing time to detect issues by 80%\n• Enabled automated retraining workflows that improved model accuracy by 15%\n• Standardized ML deployment process across 5 different departments\n• Facilitated the deployment of 20+ critical models for patient care optimization\n• Reduced infrastructure costs by 40% through intelligent autoscaling`,
      image: "https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop",
      category: "AI/ML",
      technologies: ["Kubeflow", "AWS Bedrock", "AWS SageMaker", "MLflow", "KServe", "Prometheus", "Grafana", "TensorFlow", "PyTorch", "ONNX", "Python", "Docker"],
      year: "2023",
      duration: "6 months",
      role: "Lead Platform Architect",
      teamSize: "6 engineers",
      client: "Leading Healthcare Provider"
    },
    {
      id: 3,
      title: "Hybrid Cloud Data Fabric",
      description: "A unified data access layer enabling seamless data movement and analytics across on-premises and multi-cloud environments.",
      fullDescription: `Designed and implemented a sophisticated hybrid cloud data fabric for a global retail enterprise operating in 30+ countries. The solution unified data access across their on-premises data centers, AWS, and Azure environments while enforcing consistent governance and security policies.\n\nData architecture:\n• Multi-region data virtualization layer using Red Hat OpenShift Data Foundations\n• Real-time data integration with Apache Kafka and Debezium for change data capture\n• Distributed query engine with query federation across disparate data sources\n• Metadata management layer with automated data classification and lineage tracking\n• Polyglot persistence strategy optimizing different storage technologies for specific workloads\n• Data mesh implementation with domain-oriented ownership and self-serve capabilities\n\nTechnical implementation:\n• Deployed Red Hat OpenShift Data Science platform across hybrid environments\n• Created data virtualization layer using Apache Drill and Presto for federated queries\n• Implemented Apache Kafka streams with custom connectors for real-time data integration\n• Built data transformation pipelines using Apache Spark for batch and streaming workloads\n• Established data catalog with automated discovery and classification using Apache Atlas\n• Designed unified security model with attribute-based access control\n• Implemented comprehensive data lifecycle management with tiered storage strategies\n• Created real-time analytics dashboard for business KPIs with ThoughtSpot\n\nSecurity & governance:\n• Zero-trust security architecture with end-to-end encryption for data in transit and at rest\n• Data anonymization and tokenization for sensitive information\n• Automated compliance controls for GDPR, CCPA, and industry regulations\n• Comprehensive audit logging and monitoring for data access and movements\n\nBusiness impact:\n• Reduced data integration time by 80% through automated pipelines\n• Enabled real-time inventory visibility across 2,000+ retail locations\n• Improved data analyst productivity by 65% through self-service capabilities\n• Decreased time-to-insight from weeks to hours for critical business questions\n• Supported 300+ concurrent users across analytics and data science teams\n• Generated $15M annual savings through inventory optimization using real-time data\n• Increased regulatory compliance from 75% to 99.9% through automated controls`,
      image: "/Hybrid Cloud Data Fabric.jpeg",
      category: "Data Architecture",
      technologies: ["Red Hat OpenShift Data Science", "Apache Kafka", "Debezium", "Apache Spark", "Presto", "Apache Atlas", "ThoughtSpot", "AWS S3", "Azure Data Lake", "Apache NiFi", "Delta Lake"],
      year: "2023",
      duration: "9 months",
      role: "Principal Data Architect",
      teamSize: "8 engineers",
      client: "Global Retail Enterprise"
    },
    {
      id: 4,
      title: "Zero-Trust Security Framework",
      description: "A comprehensive zero-trust security implementation for cloud-native applications with continuous verification and least-privilege access.",
      fullDescription: `Architected and implemented a comprehensive zero-trust security framework for a government agency handling sensitive data, replacing their traditional perimeter-based security model. The solution enabled secure access to cloud resources while maintaining stringent compliance requirements.\n\nSecurity architecture:\n• Defense-in-depth strategy with layered security controls\n• Identity-based perimeter using BeyondCorp principles\n• Service mesh implementation with Istio providing mTLS between all services\n• Workload identity verification using SPIFFE/SPIRE\n• Micro-segmentation with fine-grained network policies\n• Just-in-time access provisioning with automatic expiration\n• Continuous authentication and authorization at every access point\n\nTechnical implementation:\n• Implemented Istio service mesh with custom authorization policies\n• Deployed SPIFFE/SPIRE for workload identity management\n• Created attribute-based access control (ABAC) system using Open Policy Agent\n• Built centralized secrets management with HashiCorp Vault\n• Integrated with existing Keycloak deployment for identity federation\n• Implemented network micro-segmentation using Kubernetes network policies\n• Established comprehensive logging and monitoring with Falco and Wazuh\n• Created automated compliance reporting for NIST 800-53, FedRAMP, and agency-specific requirements\n\nSecurity operations:\n• Real-time threat detection with behavioral analytics\n• Automated incident response workflows for common security events\n• Continuous security posture assessment with automated remediation\n• Regular penetration testing and red team exercises\n• Security chaos engineering practices to validate controls\n\nBusiness impact:\n• Achieved FedRAMP High compliance certification 2 months ahead of schedule\n• Reduced security incident response time by 85%\n• Decreased security-related deployment delays by 90%\n• Enabled secure cloud adoption for 15 previously on-premises applications\n• Automated 95% of compliance reporting, saving 120+ hours monthly\n• Zero security breaches since implementation\n• Established repeatable pattern for zero-trust adoption across other agencies`,
      image: "/public/key and lock.jpeg",
      category: "Security",
      technologies: ["Istio", "SPIFFE/SPIRE", "Open Policy Agent", "HashiCorp Vault", "Keycloak", "Falco", "Wazuh", "Kubernetes", "AWS KMS", "Certificate Manager"],
      year: "2024",
      duration: "5 months",
      role: "Security Architect",
      teamSize: "4 engineers",
      client: "Government Agency"
    },
    {
      id: 5,
      title: "Edge Computing Platform",
      description: "A distributed edge computing platform enabling low-latency processing and real-time analytics at the network edge.",
      fullDescription: `Designed and implemented a sophisticated edge computing platform for a major industrial IoT provider, enabling real-time data processing across 1,000+ edge locations with limited connectivity and varied hardware. The solution seamlessly integrated with cloud backends while supporting autonomous operation during connectivity loss.\n\nEdge architecture:\n• Lightweight Kubernetes distribution (K3s) optimized for resource-constrained environments\n• Hierarchical topology with edge, regional, and central control planes\n• Event-driven architecture with message queuing for resilient communication\n• Edge AI capabilities with optimized machine learning inference\n• Custom container registry with delta updates for bandwidth efficiency\n• Centralized fleet management with zero-touch provisioning\n\nTechnical implementation:\n• Deployed OpenShift Edge for centralized management and orchestration\n• Implemented K3s clusters on edge devices with custom resource optimizations\n• Created MQTT-based communication fabric with store-and-forward capabilities\n• Built GitOps-based application delivery with ArgoCD edge agents\n• Developed custom operators for hardware-specific optimizations\n• Implemented TensorFlow Lite models for edge-based inference\n• Created edge-to-cloud data synchronization with conflict resolution\n• Built comprehensive observability solution with Prometheus node exporters and centralized Thanos\n\nData management:\n• Time-series database (InfluxDB) for local telemetry storage\n• Edge-optimized data compression and prioritization algorithms\n• Intelligent data routing with criticality-based forwarding\n• Local data processing with streaming analytics (Flink)\n• Automated data lifecycle management with tiered storage\n\nBusiness impact:\n• Reduced latency for critical operations from 200ms to 5ms\n• Achieved 99.999% availability for edge workloads despite intermittent connectivity\n• Decreased bandwidth consumption by 85% through edge processing\n• Enabled predictive maintenance capabilities, reducing equipment downtime by 30%\n• Supported real-time anomaly detection, preventing $2M+ in potential equipment damage\n• Scaled to 1,200+ edge locations with centralized management\n• Reduced edge deployment time from days to minutes through automation`,
      image: "public/edge computing.jpeg",
      category: "Edge Computing",
      technologies: ["K3s", "OpenShift Edge", "MQTT", "TensorFlow Lite", "ArgoCD", "Prometheus", "Thanos", "InfluxDB", "Apache Flink", "Ansible"],
      year: "2023",
      duration: "7 months",
      role: "Principal Architect",
      teamSize: "5 engineers",
      client: "Industrial IoT Provider"
    },
    {
      id: 6,
      title: "Multi-Cloud Cost Optimization",
      description: "An AI-driven platform for optimizing cloud costs across multiple providers with automated rightsizing and scheduling.",
      fullDescription: `Designed and built a comprehensive FinOps platform for a global SaaS enterprise operating across AWS, Azure, and GCP with $30M+ annual cloud spend. The solution provided real-time cost visibility, automated optimization, and intelligent forecasting capabilities.\n\nArchitectural approach:\n• Multi-cloud data collection framework with normalized taxonomy\n• Real-time cost aggregation pipeline with custom ETL processes\n• Machine learning models for usage pattern analysis and prediction\n• Rules engine for policy enforcement and automation\n• Comprehensive tagging and allocation system for accurate chargeback\n• Executive dashboards with drill-down capabilities\n\nTechnical implementation:\n• Created custom collectors for AWS Cost Explorer, Azure Cost Management, and GCP Billing APIs\n• Built data pipeline using AWS Lambda, EventBridge, and Kinesis\n• Implemented machine learning models for workload pattern recognition\n• Developed automated rightsizing engine with custom resource profiles\n• Created spot instance manager with instance interruption prediction\n• Built reserved instance and savings plan optimizer with commitment tracking\n• Implemented cloud resource scheduler with business-aware rules\n• Developed multi-dimensional anomaly detection system\n• Created custom visualization dashboards with React and D3.js\n\nAutomation capabilities:\n• Self-healing infrastructure based on policy violations\n• Automated resource hibernation for development/test environments\n• Dynamic compute scaling based on application metrics\n• Intelligent storage tiering with lifecycle policies\n• Database right-sizing with performance preservation guarantees\n• License optimization for commercial software\n\nBusiness impact:\n• Reduced overall cloud spend by 42% ($12.6M annually) without performance impact\n• Improved forecast accuracy from ±30% to ±3% for budget planning\n• Automated 85% of cost optimization activities, saving 200+ engineering hours monthly\n• Decreased time to identify cost anomalies from days to minutes\n• Enabled accurate chargeback to 25+ business units and 200+ project teams\n• Created culture of cost accountability with real-time visibility\n• Established cloud financial management as a core discipline across engineering teams`,
      image: "https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=600&fit=crop",
      category: "Cloud FinOps",
      technologies: ["AWS Cost Explorer API", "AWS Lambda", "EventBridge", "Kinesis", "Azure Cost Management", "GCP Billing API", "Python", "TensorFlow", "React", "D3.js", "PostgreSQL"],
      year: "2024",
      duration: "4 months",
      role: "Cloud Architect",
      teamSize: "3 engineers",
      client: "Enterprise SaaS Company"
    }
  ];

  // State Management
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedTechnologies, setSelectedTechnologies] = useState([]);
  const [sortBy, setSortBy] = useState('newest');
  const [isLoading, setIsLoading] = useState(false);
  const [previewProject, setPreviewProject] = useState(null);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);

  // Extract unique categories and technologies
  const categories = useMemo(() => {
    return [...new Set(mockProjects.map(project => project.category))];
  }, []);

  const technologies = useMemo(() => {
    const allTechs = mockProjects.flatMap(project => project.technologies);
    return [...new Set(allTechs)].sort();
  }, []);

  // Filter and sort projects
  const filteredProjects = useMemo(() => {
    let filtered = mockProjects;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(query) ||
        project.description.toLowerCase().includes(query) ||
        project.technologies.some(tech => tech.toLowerCase().includes(query))
      );
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }

    // Apply technology filter
    if (selectedTechnologies.length > 0) {
      filtered = filtered.filter(project =>
        selectedTechnologies.some(tech => project.technologies.includes(tech))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return parseInt(b.year) - parseInt(a.year);
        case 'oldest':
          return parseInt(a.year) - parseInt(b.year);
        case 'alphabetical':
          return a.title.localeCompare(b.title);
        case 'popularity':
          return (b.liveUrl ? 1 : 0) - (a.liveUrl ? 1 : 0);
        default:
          return 0;
      }
    });

    return filtered;
  }, [searchQuery, selectedCategory, selectedTechnologies, sortBy]);

  // Calculate active filters count
  const activeFiltersCount = useMemo(() => {
    let count = 0;
    if (selectedCategory !== 'all') count++;
    if (selectedTechnologies.length > 0) count += selectedTechnologies.length;
    return count;
  }, [selectedCategory, selectedTechnologies]);

  // Handle filter changes
  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  const handleTechnologyChange = (technologies) => {
    setSelectedTechnologies(technologies);
  };

  const handleClearFilters = () => {
    setSelectedCategory('all');
    setSelectedTechnologies([]);
    setSearchQuery('');
  };

  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  const handleSortChange = (sort) => {
    setSortBy(sort);
  };

  const handleProjectPreview = (project) => {
    setPreviewProject(project);
    setIsPreviewOpen(true);
  };

  const handleClosePreview = () => {
    setIsPreviewOpen(false);
    setPreviewProject(null);
  };

  // Simulate loading state
  useEffect(() => {
    setIsLoading(true);
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, [selectedCategory, selectedTechnologies, searchQuery, sortBy]);

  return (
    <>
      <Helmet>
        <title>Projects Portfolio - Nicholas Gerasimatos</title>
        <meta name="description" content="Explore my professional projects and work samples. Browse through web applications, mobile apps, and data science projects with detailed case studies." />
        <meta name="keywords" content="portfolio, projects, web development, mobile apps, data science, React, Node.js" />
      </Helmet>

      <div className="min-h-screen bg-background">
        <Header />
        
        <main className="pt-20 lg:pt-24">
          <div className="flex">
            {/* Desktop Filter Sidebar */}
            <FilterSection
              categories={categories}
              technologies={technologies}
              selectedCategory={selectedCategory}
              selectedTechnologies={selectedTechnologies}
              onCategoryChange={handleCategoryChange}
              onTechnologyChange={handleTechnologyChange}
              onClearFilters={handleClearFilters}
              activeFiltersCount={activeFiltersCount}
            />

            {/* Main Content */}
            <div className="flex-1 lg:pl-0">
              <div className="max-w-7xl mx-auto px-6 lg:px-12 py-8 lg:py-12">
                <Breadcrumb />
                
                {/* Page Header */}
                <div className="mb-8 lg:mb-12">
                  <h1 className="text-3xl lg:text-4xl font-bold text-text-primary mb-4">
                    My Projects
                  </h1>
                  <p className="text-lg text-text-secondary max-w-3xl">
                    Explore my professional work and personal projects. Each project represents a unique challenge 
                    and showcases different aspects of my technical expertise and problem-solving abilities.
                  </p>
                </div>

                {/* Mobile Filter Section */}
                <div className="lg:hidden">
                  <FilterSection
                    categories={categories}
                    technologies={technologies}
                    selectedCategory={selectedCategory}
                    selectedTechnologies={selectedTechnologies}
                    onCategoryChange={handleCategoryChange}
                    onTechnologyChange={handleTechnologyChange}
                    onClearFilters={handleClearFilters}
                    activeFiltersCount={activeFiltersCount}
                  />
                </div>

                {/* Search and Sort */}
                <SearchAndSort
                  searchQuery={searchQuery}
                  onSearchChange={handleSearchChange}
                  sortBy={sortBy}
                  onSortChange={handleSortChange}
                  resultsCount={filteredProjects.length}
                />

                {/* Projects Grid */}
                <ProjectGrid
                  projects={filteredProjects}
                  onProjectPreview={handleProjectPreview}
                  isLoading={isLoading}
                />
              </div>
            </div>
          </div>
        </main>

        {/* Project Preview Modal */}
        <ProjectPreviewModal
          project={previewProject}
          isOpen={isPreviewOpen}
          onClose={handleClosePreview}
        />
      </div>
    </>
  );
};

export default ProjectsPortfolio;
