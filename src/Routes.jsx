import React, { lazy, Suspense } from "react";
import { BrowserRouter, Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "components/ScrollToTop";
import ErrorBoundary from "components/ErrorBoundary";

// Lazily load page components
const PortfolioHomepage = lazy(() => import("pages/portfolio-homepage"));
const ProjectsPortfolio = lazy(() => import("pages/projects-portfolio"));
const ContactPage = lazy(() => import("pages/contact-page"));
const AboutPage = lazy(() => import("pages/about-page"));

const ProjectDetailPage = lazy(() => import("pages/project-detail-page"));
const NotFound = lazy(() => import("pages/NotFound"));

/**
 * The main routing configuration for the application.
 *
 * This component wraps the entire application with React Router and defines all
 * the top-level routes. It also includes the `ScrollToTop` component to
 * ensure that the page scrolls to the top when navigating between routes, and
 * the `ErrorBoundary` component to catch and handle any rendering errors.
 * Page components are lazily loaded to improve initial load performance.
 */
const Routes = () => {
  return (
    <BrowserRouter>
      {/* ErrorBoundary catches rendering errors in child components */}
      <ErrorBoundary>
        {/* ScrollToTop ensures that the page scrolls to the top on route changes */}
        <ScrollToTop />
        {/* Suspense provides a fallback UI while lazy-loaded components are loading */}
        <Suspense fallback={<div>Loading...</div>}> {/* You can customize this fallback */}
          {/* RouterRoutes defines the application's routes */}
          <RouterRoutes>
            {/* Route for the homepage */}
            <Route path="/" element={<PortfolioHomepage />} />
            {/* Route for the portfolio homepage */}
            <Route path="/portfolio-homepage" element={<PortfolioHomepage />} />
            {/* Route for the projects portfolio */}
            <Route path="/projects-portfolio" element={<ProjectsPortfolio />} />
            {/* Route for the contact page */}
            <Route path="/contact-page" element={<ContactPage />} />
            {/* Route for the about page */}
            <Route path="/about-page" element={<AboutPage />} />
            
            {/* Route for the project detail page */}
            <Route path="/project-detail-page" element={<ProjectDetailPage />} />
            {/* Fallback route for 404 Not Found errors */}
            <Route path="*" element={<NotFound />} />
          </RouterRoutes>
        </Suspense>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;