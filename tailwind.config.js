/** @type {import('tailwindcss').Config} */
module.exports = {
  // Configure files to scan for Tailwind classes
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html"
  ],
  // Define and extend Tailwind's default theme
  theme: {
    extend: {
      // Custom color palette
      colors: {
        // Primary Colors: Used for main branding elements, prominent CTAs, and key text.
        'primary': '#1a1a1a', // deep-charcoal
        'primary-foreground': '#ffffff', // white
        
        // Secondary Colors: Used for less prominent actions, secondary backgrounds, or subtle accents.
        'secondary': '#4a5568', // sophisticated-gray
        'secondary-foreground': '#ffffff', // white
        
        // Accent Colors: Used for interactive elements, highlights, and to draw attention.
        'accent': '#3182ce', // professional-blue
        'accent-foreground': '#ffffff', // white
        
        // Background Colors: Define the main background and elevated surface colors.
        'background': '#ffffff', // pure-white
        'surface': '#f7fafc', // subtle-off-white
        
        // Text Colors: For various levels of text hierarchy.
        'text-primary': '#2d3748', // rich-dark-gray
        'text-secondary': '#718096', // balanced-medium-gray
        
        // Status Colors: For conveying success, warning, and error states.
        'success': '#38a169', // professional-green
        'success-foreground': '#ffffff', // white
        'warning': '#d69e2e', // sophisticated-amber
        'warning-foreground': '#ffffff', // white
        'error': '#e53e3e', // clear-red
        'error-foreground': '#ffffff', // white
        
        // Border Color
        'border': 'rgba(0, 0, 0, 0.1)', // subtle-border
      },
      // Custom font families
      fontFamily: {
        'heading': ['Inter', 'system-ui', 'sans-serif'],
        'body': ['Inter', 'system-ui', 'sans-serif'],
        'caption': ['Inter', 'system-ui', 'sans-serif'],
        'mono': ['JetBrains Mono', 'Consolas', 'monospace'],
      },
      // Custom font weights
      fontWeight: {
        'normal': '400',
        'medium': '500',
        'semibold': '600',
        'bold': '700',
      },
      // Custom border-radius values
      borderRadius: {
        'sm': '6px',
        'md': '8px',
        'lg': '12px',
      },
      // Custom box shadows
      boxShadow: {
        'card': '0 1px 3px rgba(0, 0, 0, 0.1)',
        'interactive': '0 4px 6px rgba(0, 0, 0, 0.1)',
        'nav': '0 1px 3px rgba(0, 0, 0, 0.1)',
      },
      // Custom transition timing functions
      transitionTimingFunction: {
        'smooth': 'cubic-bezier(0.4, 0, 0.2, 1)',
      },
      // Custom transition durations
      transitionDuration: {
        'fast': '200ms',
        'normal': '300ms',
      },
      // Custom spacing values
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      // Custom z-index values
      zIndex: {
        '100': '100',
        '150': '150',
        '200': '200',
      },
      // Custom animations
      animation: {
        'shimmer': 'shimmer 2s linear infinite',
      },
      // Custom keyframes for animations
      keyframes: {
        shimmer: {
          '0%': { transform: 'translateX(-100%)' },
          '100%': { transform: 'translateX(100%)' },
        },
      },
    },
  },
  // Add Tailwind CSS plugins
  plugins: [
    require('@tailwindcss/typography'),
    require('tailwindcss-animate'),
  ],
}