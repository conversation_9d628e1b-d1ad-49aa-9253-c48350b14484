import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tsconfigPaths from "vite-tsconfig-paths";
import tagger from "@dhiwise/component-tagger";
import { visualizer } from "rollup-plugin-visualizer";

// Vite configuration: https://vitejs.dev/config/
export default defineConfig({
  // Build configuration options
  build: {
    // Output directory for the build files
    outDir: "build",
    // Warn if a chunk exceeds 2000 kB (2MB) to help with performance optimization
    chunkSizeWarningLimit: 2000,
  },
  // Plugins to extend Vite's capabilities
  plugins: [
    tsconfigPaths(), // Enables TypeScript path aliases to work in Vite
    react(),         // Provides React refresh and other React-specific features
    tagger(),        // Custom plugin for component tagging (likely for DhiWise integration)
    visualizer({      // Generates a bundle analysis report
      filename: "bundle-analysis.html",
      open: true,
    }),
  ],
  // Development server options
  server: {
    // Port to run the development server on
    port: "4028",
    // Host to bind the server to (0.0.0.0 makes it accessible externally)
    host: "0.0.0.0",
    // Exit if the port is already in use
    strictPort: true,
    // List of hosts that are allowed to access the development server
    allowedHosts: ['.amazonaws.com', '.builtwithrocket.new']
  }
});